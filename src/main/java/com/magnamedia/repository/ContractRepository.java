package com.magnamedia.repository;

import com.magnamedia.core.repository.BaseRepository;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.module.type.ContractStatus;
import com.magnamedia.module.type.DirectDebitStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Repository
public interface ContractRepository extends BaseRepository<Contract> {

    Contract findFirstOneByHousemaidOrderByCreationDateDesc(Housemaid housemaid);

    Contract findFirstOneByHousemaid_IdAndStatusOrderByCreationDateDesc(Long housemaidId, ContractStatus status);

    Contract findFirstOneByHousemaidAndStatusAndContractProspectType_CodeOrderByCreationDateDesc(
            Housemaid housemaid, ContractStatus status, String prospectType);

    Contract findTop1ByHousemaidAndStatus(Housemaid housemaid, ContractStatus status);

    //Jirra ACC-1087
    List<Contract> findByClientOrderByCreationDateDesc(Client client);

    Contract findTopByClientOrderByCreationDateDesc(Client client);

    List<Contract> findByHousemaidAndStatus(Housemaid housemaid, ContractStatus status);

    // ACC-837
    // ACC-1241
    Contract findFirstOneByClientAndStatusInOrderByCreationDateDesc(Client client, ContractStatus[] statuses);

    @Query("SELECT c "
            + "FROM Contract c "
            + "WHERE "
            + "(SELECT count(l) from LiveInOutLog l where l.reason = com.magnamedia.module.type.LiveInOutLogReason.START_OF_CONTRACT and l.contract=c)>1")
    List<Contract> findInvalidMoreThanOneLog();

    @Query("SELECT c from Contract c where "
            + "(SELECT l.contract from LiveInOutLog l where l.reason = com.magnamedia.module.type.LiveInOutLogReason.START_OF_CONTRACT and l.contract=c group by l.contract) IS NULL "
            + "and c.status<> com.magnamedia.module.type.ContractStatus.PLANNED_RENEWAL")
    List<Contract> findInvalidNoStartLog();

    @Query("SELECT c "
            + "FROM Contract c "
            + "WHERE c.status =com.magnamedia.module.type.ContractStatus.ACTIVE AND c.creationDate IS NULL")
    List<Contract> findActiveContractsWithNullCreationDate();

    @Query("SELECT coalesce(max(ch.id), 0) FROM Contract ch")
    Long getMaxId();

    List<Contract> findByHousemaid(Housemaid housemaid);

    // ACC-1241
    @Query("SELECT C FROM Contract C "
            + "INNER JOIN Attachment A ON (A.ownerId = C.id AND A.ownerType = 'Contract' AND A.tag LIKE ?1) "
            + "where C.discountCode <> null AND C.client <> null AND C.refundSentToExpensify = false AND C.id not in ?2")
    Page<Contract> findByDiscountCodeNotNullAndClientNotNullAndAttachmentsTagEquals(String tag, List<Long> ids, Pageable pageable);

    // ACC-1435
    @Query(value = "SELECT cont FROM Contract cont WHERE cont.client = :client AND cont.status= 'ACTIVE'")
    List<Contract> findActiveContractByClient(@Param("client") Client c);

    @Query(value = "SELECT cont FROM Contract cont WHERE cont.client = :client")
    List<Contract> findAllContractByClient(@Param("client") Client c);

    boolean existsByStatusAndClient(ContractStatus status, Client client);

    @Query(nativeQuery = true,value = "SELECT id FROM CONTRACTS c WHERE c.id LIKE CONCAT(?1 , '%')")
    List<Long> findByIdStartWith(String id, Pageable pageable);

    @Query("select c from Contract c " +
            "left join c.housemaid h "+
            "where c.id > ?1 and c.status = 'ACTIVE' and c.scheduledDateOfTermination is null and " +
                "(h is null or (h.travelAssist is null or h.travelAssist = false)) and " +
                "(c.paidEndDate between ?2 AND ?3) and c.payingViaCreditCard = false and " +
                "c.excludeFromExpiryFlow = false and c.client.vip = false and c.client.vVip = false and " +
                "not exists( select 1 from FlowProcessorEntity f " +
                            "where f.contractPaymentTerm.contract.id = c.id and " +
                                "f.flowEventConfig.name in ?4 and f.stopped = false and f.completed = false)")
    Page<Contract> findForPaymentExpiryFlow(long lastId, Date startDate, Date endDate, List<FlowEventConfig.FlowEventName> eventNames, Pageable pageable);

    List<Contract> findByScheduledDateOfTerminationAndStatusNotInAndContractProspectType_Code(
            Date firstDate, List<ContractStatus> statuses, String code);

    @Query(nativeQuery = true, value =
            "select count(c.ID) from CONTRACTS c " +
            "inner join PICKLISTS_ITEMS p on c.CONTRACT_PROSPECT_TYPE_ID = p.id " +
            "where c.id = ?1 and " +
                "((p.CODE = 'maids.cc_prospect' and " +
                    "exists( select 1 from CCSERVICEAPPLICATIONS_REVISIONS ca where ca.CONTRACT_ID = c.id and ca.EXISTING_CLIENT_FLOW = 1)) or " +
                "(p.CODE = 'maidvisa.ae_prospect' and " +
                    "exists( select 1 from VISASERVICEAPPLICATIONS_REVISIONS va where va.CONTRACT_ID = c.id and va.EXISTING_CLIENT_FLOW = 1)))")
    int contractForExistingClientFlow(Long contractId);

    @Query(nativeQuery = true, value =
            "select count(c.ID) from CONTRACTS c " +
            "inner join PICKLISTS_ITEMS p on c.CONTRACT_PROSPECT_TYPE_ID = p.id " +
            "where c.id = ?1 and " +
                "((p.CODE = 'maids.cc_prospect' and " +
                    "exists( select 1 from CCSERVICEAPPLICATIONS ca where ca.CONTRACT_ID = c.id and ca.SOURCE = 'CLIENT_MOBILE_APP')) or " +
                "(p.CODE = 'maidvisa.ae_prospect' and " +
                    "exists( select 1 from VISASERVICEAPPLICATIONS va where va.CONTRACT_ID = c.id and va.SOURCE = 'CLIENT_MOBILE_APP')))")
    int contractForExistingClientFlowBySourceClientMobileApp(Long contractId);

    @Query("select c from Contract c " +
            "where c.id > ?1 and c.payingViaCreditCard = true and " +
                "not exists( select 1 from AccountingEntityProperty a " +
                "           where a.originId = c.id and a.originType = 'Contract' and a.key = 'change_to_paying_via_credit_card_date')")
    Page<Contract> findContractPayingViaCcForAddAccountingProperty(Long lastId, Pageable pageable);

    @Query(nativeQuery = true,
            value = "SELECT CR.REVISION FROM CONTRACTS_REVISIONS CR " +
                    "WHERE CR.ID = ?1 AND CR.PAYING_VIA_CREDIT_CARD = 0 " +
                    "ORDER by CR.LAST_MODIFICATION_DATE DESC " +
                    "limit 1")
    Long findContractLastRevisionNotFlaggedASPayingViaCc(Long id);

    @Query(nativeQuery = true,
            value = "SELECT CR.LAST_MODIFICATION_DATE FROM CONTRACTS_REVISIONS CR " +
                    "WHERE CR.ID = ?1 AND CR.REVISION > ?2 AND CR.PAYING_VIA_CREDIT_CARD = 1 " +
                    "ORDER by CR.LAST_MODIFICATION_DATE ASC " +
                    "limit 1")
    Date findContractFirstRevisionFlaggedASPayingViaCcAfterRevision(Long id, Long revision);

    @Query("select new map(sum(case when c.contractProspectType.code = 'maids.cc_prospect' then 1 else 0 end ) as ccContractsCount, " +
            "sum(case when c.contractProspectType.code = 'maidvisa.ae_prospect' then 1 else 0 end ) as mvContractsCount) " +
            "from Contract c " +
            "where c.status = 'ACTIVE' or " +
            "exists( select 1 from Payment p " +
                    "where p.contract = c and p.status = 'BOUNCED' and p.dateOfPayment between ?1 and ?2 and " +
                        "p.typeOfPayment.code = 'monthly_payment' and p.replaced = false)")
    Map<String, Long> getTheCountOfActiveContractByContractProspectType(Date startOfMonth, Date endOfMonth);

    Page<Contract> findByIdIn(List<Long> contractIds, Pageable pageable);

    @Query("select c from DirectDebit dd " +
            "join dd.contractPaymentTerm.contract c " +
            "where c.id > ?1 and c.status in ('CANCELLED', 'EXPIRED') and " +
            "((dd.category = 'A' and dd.MStatus not in ?2) or " +
            "(dd.category = 'B' and dd.status not in ?2)) and " +
            "c.contractProspectType.code = 'maidvisa.ae_prospect' and " +
            "not exists (select 1 from Payment p " +
            "where p.contract = c and (p.requiredForBouncing = true or p.requiredForUnfitToWork = true)) and " +
            "not exists (select 1 from DirectDebitCancelationToDo todo " +
            "where todo.directDebit = dd and todo.stopped = false and todo.completed = false) " +
            "group by c.id")
    Page<Contract> findCancelledMVContractsWithNoRequiredPayments(Long id, List<DirectDebitStatus> l, Pageable pageable);

    @Query("select c from Contract c " +
            "where c.id > ?1 and c.contractProspectType.code = 'maids.cc_prospect' and " +
            "c.payingViaCreditCard = true and c.status = 'ACTIVE'")
    Page<Contract> findActiveCcAndPayingViaCcContract(Long id, Pageable pageable);

    @Query("select c from Contract c " +
            "join ContractPaymentTerm cpt on cpt.contract = c and cpt.isActive = true " +
            "inner join ContractPaymentType t on t.contractPaymentTerm = cpt " +
            "join c.housemaid h " +
            "where c.id > ?1 and c.contractProspectType.code = 'maidvisa.ae_prospect' and c.startOfContract <= ?2 and " +
            "c.payingViaCreditCard = false and c.status = 'ACTIVE' and " +
            "t.type.code = 'monthly_payment' and " +
            "exists (select 1 from Payment p " +
                        "where p.contract = c and p.amountOfPayment > 0 and p.status = 'RECEIVED' and " +
                                "p.typeOfPayment.code in ('pre_collected_payment', 'pre_collected_payment_no_vat')) and " +
            "exists (select 1 from BaseAdditionalInfo b " +
                        "where b.ownerId = c.id and b.ownerType = 'Contract' and " +
                            "b.infoKey = 'preCollectedSalary' and b.infoValue = 'true') and " +
            "not exists (select 1 from BaseAdditionalInfo b " +
                        "where b.ownerId = c.id and b.ownerType = 'Contract' and " +
                            "b.infoKey in ('passedMedicalStep', 'isIpamRunning') and b.infoValue = 'true') and " +
            "not exists (select 1 from BaseAdditionalInfo b " +
                                "where b.ownerId = h.id and b.ownerType = 'Housemaid' and " +
                                "b.infoKey = 'hasMedicalCheckThisMonth' and b.infoValue = ?3) and " +
            "not exists (select 1 from Payment p " +
                        "where p.contract = c and p.status = 'RECEIVED' and p.typeOfPayment.code = 'monthly_payment' and " +
                                "p.dateOfPayment >= ?4 and p.dateOfPayment <= ?5) " +
            "order by c.id asc")
    Page<Contract> findActiveMVPreCollectedContractsWithPreCollectedPayments(
            Long id, Date start, String lastPassedDate, Date nextMonthStart, Date nextMonthEnd, Pageable pageable);

    @Query("select c from Contract c " +
            "join ContractPaymentTerm cpt on cpt.contract = c and cpt.isActive = true " +
            "inner join ContractPaymentType t on t.contractPaymentTerm = cpt " +
            "join c.housemaid h " +
            "where c.id > ?1 and c.contractProspectType.code = 'maidvisa.ae_prospect' and c.startOfContract <= ?2 and " +
            "c.payingViaCreditCard = false and c.status = 'ACTIVE' and t.discountEffectiveAfter < ?3 and " +
            "t.type.code = 'monthly_payment' and " +
            "exists (select 1 from BaseAdditionalInfo b " +
                        "where b.ownerId = c.id and b.ownerType = 'Contract' and " +
                            "b.infoKey = 'preCollectedSalary' and b.infoValue = 'true') and " +
            "not exists (select 1 from BaseAdditionalInfo b " +
                        "where b.ownerId = c.id and b.ownerType = 'Contract' and " +
                            "b.infoKey in ('passedMedicalStep', 'isIpamRunning') and b.infoValue = 'true') and " +
            "not exists (select 1 from BaseAdditionalInfo b " +
                                "where b.ownerId = h.id and b.ownerType = 'Housemaid' and " +
                                "b.infoKey = 'hasMedicalCheckThisMonth' and b.infoValue = ?4) and " +
            "not exists (select 1 from Payment p " +
                            "where p.contract = c and p.status = 'RECEIVED' and p.typeOfPayment.code = 'monthly_payment' and " +
                            "p.dateOfPayment >= ?5 and p.dateOfPayment <= ?6) " +
            "order by c.id asc")
    Page<Contract> findActiveMVPreCollectedContractsWithoutPreCollectedPayments(
            Long id, Date start, int threshold, String lastPassedDate, Date nextMonthStart, Date nextMonthEnd, Pageable pageable);

    @Query("select distinct c from DirectDebit dd " +
            "join dd.contractPaymentTerm.contract c " +
            "left join DirectDebitFile ddf on ddf.directDebit = dd " +
            "where c.id > ?1 and c.status in ?2 and " +
                "((dd.category = 'A' and dd.MStatus not in ?3) or (dd.category = 'B' and dd.status not in ?4)) and " +
                        "(ddf is null or not exists (select 1 from DirectDebitCancelationToDo todo " +
                            "where todo.directDebitFile = ddf and todo.stopped = false and todo.completed = false)) and " +
                "not exists (select 1 from BaseAdditionalInfo b " +
                                "where b.ownerId = c.id and b.ownerType = 'Contract' and " +
                                    "b.infoKey = ?5 and b.infoValue = 'true')")
    Page<Contract> findAllByActiveDdAndNotReceivedAboutPaymentDeductionBefore(
            Long lastId, List<ContractStatus> statuses, List<DirectDebitStatus> notActiveMStatus,
            List<DirectDebitStatus> notActiveStatus, String additionalInfoKey, Pageable pageable);

    @Query("select distinct c from DirectDebit dd " +
            "join dd.contractPaymentTerm.contract c " +
            "join DirectDebitFile ddf on ddf.directDebit = dd " +
            "where c.id > ?1 and c.id not in ?2 and c.status in ?3 and " +
                "ddf.ddStatus = 'PENDING_FOR_CANCELLATION' and " +
                "not exists (select 1 from BaseAdditionalInfo b " +
                                "where b.ownerId = c.id and b.ownerType = 'Contract' and " +
                                    "b.infoKey = ?4 and b.infoValue = 'true')")
    Page<Contract> findAllByPendingDDsAndNotReceivedAboutPaymentDeduction(
            Long lastId, List<Long> contractsActiveDDsAfterCancellation,
            List<ContractStatus> statuses, String additionalInfoKey, Pageable pageable);
}