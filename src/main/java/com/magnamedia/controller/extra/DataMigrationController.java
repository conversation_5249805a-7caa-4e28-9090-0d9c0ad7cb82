package com.magnamedia.controller.extra;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.magnamedia.controller.BucketsController;
import com.magnamedia.controller.ExpensesController;
import com.magnamedia.controller.SupplierController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Picklist;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.helper.BackgroundTaskService;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.repository.PicklistRepository;
import com.magnamedia.core.repository.TemplateRepository;
import com.magnamedia.core.repository.UserRepository;
import com.magnamedia.entity.*;
import com.magnamedia.extra.*;
import com.magnamedia.extra.ExpenseFlow.ExpenseCsvDTO;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.helper.PicklistHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.helper.PaginationHelper;
import com.magnamedia.repository.*;
import com.magnamedia.service.DataMigrationService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Created by Mamon.Masod on 4/13/2021.
 */
@RequestMapping("/data-migration")
@RestController
public class DataMigrationController {

    protected static final Logger logger = Logger.getLogger(DataMigrationController.class.getName());

    @Autowired
    private Utils utils;
    @Autowired
    private BucketRepository bucketRepo;
    @Autowired
    private BucketsController bucketCtrl;
    @Autowired
    private SupplierRepository supplierRepo;
    @Autowired
    private SupplierController supplierCtrl;
    @Autowired
    private ExpenseRepository expenseRepo;
    @Autowired
    private ExpensesController expensesCtrl;
    @Autowired
    private UserRepository userRepo;
    @Autowired
    private BackgroundTaskService backgroundTaskService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private DataMigrationController selfCtrl;
    @Autowired
    private DataMigrationService dataMigrationService;
    @Autowired
    private HousemaidTransactionRepository housemaidTransactionRepository;

    @PreAuthorize("hasPermission('data-migration','acc-3341/migrate-buckets')")
    @RequestMapping(value = "/acc-3341/migrate-buckets", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity dataMigration_ACC_3341_migrateBuckets(@RequestParam(name = "file", required = false) MultipartFile csvFile) throws Exception {
        String[] columns = new String[]{"Name of Bucket", "Code", "Bucket Holder ID", "Bucket Type", "Initial Balance", "Wealth Bucket Type",
                "Activate Auto Replenishment", "Replenishment Balance", "Balance after Replenishment", "Refill From Which Bucket Code", "Use Trans Guard Service"};

        List<BucketWrapper> bucketWrapperList = CsvHelper.readCsv(utils.getInputStreamFromAttachmentOrMultiPartFile(csvFile), BucketWrapper.class, columns, true);

        for (BucketWrapper bucketWrapper : bucketWrapperList) {
            Bucket bucket = bucketRepo.findByCode(bucketWrapper.getCode());

            if (bucket == null) {
                bucket = new Bucket();
                bucket.setCode(bucketWrapper.getCode());
            }

            bucket.setName(bucketWrapper.getNameOfBucket());

            if (bucketWrapper.getBucketHolderId() != null) {
                bucket.setHolder(userRepo.findOne(bucketWrapper.getBucketHolderId()));
            }

            if (bucketWrapper.getBucketTypeAsEnum() != null) {
                bucket.setBucketType(bucketWrapper.getBucketTypeAsEnum());
            }

            if (bucketWrapper.getInitialBalance() != null) {
                bucket.setInitialBalance(bucketWrapper.getInitialBalance());
            }

            if (bucketWrapper.getWealthBucketTypeAsEnum() != null) {
                bucket.setWealthBucketType(bucketWrapper.getWealthBucketTypeAsEnum());
            }

            if (bucketWrapper.getActivateAutoReplenishment() != null) {
                bucket.setAutoReplenishment(bucketWrapper.getActivateAutoReplenishment());
            }

            if (bucketWrapper.getReplenishmentBalance() != null) {
                bucket.setReplenishmentLevel(bucketWrapper.getReplenishmentBalance());
            }

            if (bucketWrapper.getBalanceAfterReplenishment() != null) {
                bucket.setLevelAfterReplenishment(bucketWrapper.getBalanceAfterReplenishment());
            }

            if (bucketWrapper.getRefillFromWhichBucketCode() != null) {
                bucket.setRefillerBucket(bucketRepo.findByCode(bucketWrapper.getRefillFromWhichBucketCode()));
            }

            if (bucketWrapper.getUseTransguardService() != null) {
                bucket.setTransGuardService(bucketWrapper.getUseTransguardService());
            }

            bucketCtrl.createEntity(bucket);
        }

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('data-migration','acc-3341/migrate-suppliers/not-linked-to-sales')")
    @RequestMapping(value = "/acc-3341/migrate-suppliers/not-linked-to-sales", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity dataMigration_ACC_3341_migrateSuppliersNotLinkedToSales(@RequestParam(name = "file", required = false) MultipartFile csvFile) throws Exception {
        String[] columns = new String[]{"Supplier Name", "Supplier location", "Supplier Phone Number", "Supplier Website", "Supplier Email", "Vat Registered",
                "Payment Method", "Mobile Number", "Local Account Name", "Local Account Number", "Local IBAN", "International Account Name", "International Account Number",
                "International IBAN", "International Swift Code", "International Address", "Name in Bank SOA"};

        List<SupplierCsvNotLinkedToSalesDTO> supplierDTOList = CsvHelper.readCsv(utils.getInputStreamFromAttachmentOrMultiPartFile(csvFile), SupplierCsvNotLinkedToSalesDTO.class, columns, true);

        for (SupplierCsvNotLinkedToSalesDTO supplierCsvDTO : supplierDTOList) {
            Supplier supplier = null;
            if (supplierCsvDTO.getSupplierName() != null && !supplierCsvDTO.getSupplierName().isEmpty()) {
                supplier = supplierRepo.findFirstByName(supplierCsvDTO.getSupplierName());
            }

            if (supplier == null) {
                supplier = new Supplier();
            }

            supplierCsvDTO.fillSupplierProps(supplier);

            supplierCtrl.createEntity(supplier);
        }

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('data-migration','acc-3341/migrate-suppliers/linked-to-sales')")
    @RequestMapping(value = "/acc-3341/migrate-suppliers/linked-to-sales", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity dataMigration_ACC_3341_migrateSuppliersLinkedToSales(@RequestParam(name = "file", required = false) MultipartFile csvFile) throws Exception {
        String[] columns = new String[]{"Sales binder ID", "Supplier Name", "Supplier location", "Supplier Phone Number", "Supplier Website", "Supplier Email", "Vat Registered",
                "Payment Method", "Mobile Number", "Local Account Name", "Local Account Number", "Local IBAN", "International Account Name", "International Account Number",
                "International IBAN", "International Swift Code", "International Address", "Name in Bank SOA"};

        List<SupplierCsvLinkedToSalesDTO> supplierDTOList = CsvHelper.readCsv(utils.getInputStreamFromAttachmentOrMultiPartFile(csvFile), SupplierCsvLinkedToSalesDTO.class, columns, true);

        for (SupplierCsvLinkedToSalesDTO supplierCsvDTO : supplierDTOList) {
            Supplier supplier = null;
            if (supplierCsvDTO.getSalesBinderId() != null && !supplierCsvDTO.getSalesBinderId().isEmpty()) {
                supplier = supplierRepo.findBySupplierId(supplierCsvDTO.getSalesBinderId());
            }

            if (supplier == null) {
                supplier = new Supplier();
                supplier.setSupplierId(supplierCsvDTO.getSalesBinderId());
            }

            supplierCsvDTO.fillSupplierProps(supplier);

            supplierRepo.save(supplier);
        }

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('data-migration','acc-3341/migrate-expenses')")
    @RequestMapping(value = "/acc-3341/migrate-expenses", method = RequestMethod.POST)
    @Transactional
    public ResponseEntity dataMigration_ACC_3341_migrateExpenses(@RequestParam(name = "file", required = false) MultipartFile csvFile) throws Exception {
        String[] columns = new String[]{"Old expense name", "Old Expense code", "New expense name", "New expense code", "Expense Caption", "Sub Expense",
                "Parent Expense", "Expense Manager", "Requested From", "Needs Attachment", "Requires invoice", "Requesters", "Approval Method",
                "Approve Holder Type", "Approved by User", "Approved by Email", "Approved by Final Manager", "limit For Approval", "Enable COO Approval Limit",
                "Limit For COO Approval", "Expense Payment Method", "Paid on Spot Cash", "Paid on Spot Card", "Cash Bucket", "Card Bucket", "Addition Reason",
                "Allow to add Loan", "Loan Type", "Default Amount for Expense", "Beneficiary Type", "Suppliers", "Related to Maid", "Related to Applicant",
                "Related to Office Staff", "Related to Team", "Expense Name of Maid CC", "Expense Name of Maid Visa", "Expense Name of Applicant",
                "Expense Name of Office Staff", "Expense Name of Team", "Auto Deducted", "Allow to Create Sub Expense from Request Expenses Page",
                "Allow to Add Supplier in Expense Request"};

        List<ExpenseCsvDTO> expenseDTOList = CsvHelper.readCsv(utils.getInputStreamFromAttachmentOrMultiPartFile(csvFile), ExpenseCsvDTO.class, columns, true);

        for (ExpenseCsvDTO expenseCsvDTO : expenseDTOList) {
            selfCtrl.updateExpenseBaseConfirmation(expenseCsvDTO);
        }

        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        for (ExpenseCsvDTO expenseCsvDTO : expenseDTOList) {
            Long time = new Date().getTime();

            backgroundTaskService.addDirectCallBackgroundTaskForEntity(
                    "Accounting_DataMigration_3341_" + expenseCsvDTO.getExpenseGeneratedWithCode() + "_" + time,
                    "dataMigrationController",
                    "accounting",
                    "processExpense",
                    "Expense",
                    null,
                    true,
                    false,
                    new Class<?>[]{Map.class},
                    new Object[]{objectMapper.convertValue(expenseCsvDTO, Map.class)});
        }

        return ResponseEntity.ok("Done");
    }

    @Transactional
    public boolean processExpense(Map map) {

        ExpenseCsvDTO expenseCsvDTO = objectMapper.convertValue(map, ExpenseCsvDTO.class);
        Expense expense = expenseRepo.findOneByCode(expenseCsvDTO.getExpenseGeneratedWithCode());

        expensesCtrl.createEntity(expenseCsvDTO.fillExpenseProps(expense));

        return true;
    }

    public void updateExpenseBaseConfirmation(ExpenseCsvDTO expenseCsvDTO) {
        Expense expense;

        if (!StringUtils.isEmpty(expenseCsvDTO.getOldExpenseCode())) {
            expense = expenseRepo.findOneByCode(expenseCsvDTO.getOldExpenseCode());
            if (expense == null) {
                throw new RuntimeException(String.format("Expense with code '%s' doesn't exists", expenseCsvDTO.getOldExpenseCode()));
            }
        } else {
            expense = new Expense();
        }

        Expense parentExpense = expenseCsvDTO.getParentExpenseAsExpense();
        if (parentExpense != null) {
            expense.setParent(parentExpense);
        }

        expense.setCode(expenseCsvDTO.getNewExpenseCode());
        expense.setName(expenseCsvDTO.getNewExpenseName());
        expense.setCaption(expenseCsvDTO.getExpenseCaption());

        expensesCtrl.createEntity(expense);

        logger.info("Expense Generated with Code: " + expense.getCode());
        expenseCsvDTO.setExpenseGeneratedWithCode(expense.getCode());
    }

    @PreAuthorize("hasPermission('data-migration','acc-3490/migrate-purchasing-flow-items')")
    @RequestMapping(value = "/acc-3490/migrate-purchasing-flow-items", method = RequestMethod.POST)
    public ResponseEntity dataMigration_ACC_3490_migrateItemsOfPurchasingFlow(@RequestParam(name = "file", required = false) MultipartFile csvFile) throws Exception {
        String[] columns = new String[]{"Category ID", "Category Name", "Item ID", "Item Name", "Measure of Consumption", "Consumption Rate", "Unit", "Cycle"};

        List<PurchasingCategoryItemDTO> purchasingCategoryItemDTOS = CsvHelper.readCsv(utils.getInputStreamFromAttachmentOrMultiPartFile(csvFile), PurchasingCategoryItemDTO.class, columns, true);

        for (PurchasingCategoryItemDTO purchasingCategoryItemDTO : purchasingCategoryItemDTOS) {
            Long time = new Date().getTime();

            backgroundTaskService.addDirectCallBackgroundTaskForEntity(
                    "Accounting_DataMigration_3490_Category" + purchasingCategoryItemDTO.getCategoryName() + "_Item_" + purchasingCategoryItemDTO.getItemName() + "_" + time,
                    "dataMigrationController",
                    "accounting",
                    "processPurchasing",
                    "Category_Item",
                    null,
                    true,
                    false,
                    new Class<?>[]{Map.class},
                    new Object[]{objectMapper.convertValue(purchasingCategoryItemDTO, Map.class)});
        }

        return ResponseEntity.ok("Done");
    }

    @Autowired
    private CategoryRepository categoryRepository;

    @Autowired
    private ItemRepository itemRepository;

    @Transactional
    public boolean processPurchasing(Map map) {
        PurchasingCategoryItemDTO purchasingCategoryItemDTO = objectMapper.convertValue(map, PurchasingCategoryItemDTO.class);
        Category category = purchasingCategoryItemDTO.getCategory();
        category.setOrderCycle(purchasingCategoryItemDTO.getCycleAsPickListItem());
        categoryRepository.save(category);

        Item item = purchasingCategoryItemDTO.getItem();
        item.setMeasureOfConsumption(purchasingCategoryItemDTO.getMeasureOfConsumptionAsPickListItem());
        item.setConsumptionRate(BigDecimal.valueOf(purchasingCategoryItemDTO.getConsumptionRate()));
        itemRepository.save(item);

        return true;
    }

    @Autowired
    private TemplateRepository templateRepository;

    @PreAuthorize("hasPermission('data-migration','CMA-628')")
    @RequestMapping(path = "/CMA-628", method = RequestMethod.GET)
    public ResponseEntity cma628() {
        List<String> templatesNames = Arrays.asList("Accounting_Payment_ProofWithLink_Of_Transfer_To_Client", "Accounting_Owe_Money_To_Client",
                "Accounting_Not_Owed_Money_From_Client", "Accounting_Wrongly_Charged_Money_On_Client");

        for (String templateNAme : templatesNames) {

            Template notificationTemplate = TemplateUtil.getTemplate(templateNAme);

            Template smsTemplate = new Template("", "", "", "");

            BeanUtils.copyProperties(notificationTemplate, smsTemplate);

            smsTemplate.setId(null);

            String smsTemplateUniqueName = notificationTemplate.getName() + "_SMS";

            smsTemplate.setName(smsTemplateUniqueName);
            TemplateUtil.createTemplate(smsTemplate, new HashMap());

            notificationTemplate.setNotificationSmsTemplateName(smsTemplate.getName());
            notificationTemplate.setDescription("this is a notification template that is linked with sms template:" + smsTemplate.getId());

            templateRepository.save(notificationTemplate);

        }

        return new ResponseEntity("Done", HttpStatus.OK);
    }

    // ACC-8444
    @PreAuthorize("hasPermission('data-migration','handlePostponedPlanPendingPayingViaCreditCard')")
    @GetMapping(path = "/handlePostponedPlanPendingPayingViaCreditCardAcc8444")
    public ResponseEntity<?> handlePostponedPlanPendingPayingViaCreditCardAcc8444(
            String email,
            @RequestParam(name = "withOutSave", required = false, defaultValue = "false") boolean withOutSave) throws IOException {

        dataMigrationService.sendEmailAcc8444(email, dataMigrationService.handlePostponedPlanPendingPayingViaCreditCardAcc8444(withOutSave));
        return ResponseEntity.ok("Done");
    }

    // ACC-7580
    @PreAuthorize("hasPermission('data-migration','migratePicklistsForRiskDocumentsMgmt')")
    @GetMapping(path = "/migratePicklistsForRiskDocumentsMgmt")
    public ResponseEntity<?> migratePicklistsForRiskDocumentsMgmt() {

        PicklistItem types = PicklistHelper
                .getItemNoException(AccountingModule.PICKLIST_RISK_DOCUMENT_MANAGEMENT_TYPE, "agreement");

        PicklistItem imporatance = PicklistHelper
                .getItemNoException(AccountingModule.PICKLIST_RISK_DOCUMENT_MANAGEMENT_IMPORTANCE, "catastrophic");

        PicklistItem govEntity = PicklistHelper
                .getItemNoException(AccountingModule.PICKLIST_RISK_DOCUMENT_MANAGEMENT_GOVERNMENT_ENTITY, "ded");

        PicklistRepository picklistRepository = Setup.getRepository(PicklistRepository.class);
        if (types == null) {
            Picklist picklist = new Picklist(
                    AccountingModule.PICKLIST_RISK_DOCUMENT_MANAGEMENT_TYPE,
                    "Risk Document Management Type");

            picklist.addItem(new PicklistItem(picklist, "Agreement", "agreement"));
            picklist.addItem(new PicklistItem(picklist, "Bank Documents", "bank_documents"));
            picklist.addItem(new PicklistItem(picklist, "Contract", "contract"));
            picklist.addItem(new PicklistItem(picklist, "Document", "document"));
            picklist.addItem(new PicklistItem(picklist, "Kiosk", "kiosk"));
            picklist.addItem(new PicklistItem(picklist, "License", "license"));
            picklist.addItem(new PicklistItem(picklist, "PRO Cards", "pro_cards"));
            picklist.addItem(new PicklistItem(picklist, "Vehicles", "vehicles"));
            picklistRepository.save(picklist);
        }

        if (imporatance == null) {
            Picklist picklist = new Picklist(
                    AccountingModule.PICKLIST_RISK_DOCUMENT_MANAGEMENT_IMPORTANCE,
                    "Risk Document Management Importance");

            picklist.addItem(new PicklistItem(picklist, "Catastrophic", "catastrophic"));
            picklist.addItem(new PicklistItem(picklist, "High", "high"));
            picklist.addItem(new PicklistItem(picklist, "Medium", "medium"));
            picklistRepository.save(picklist);
        }

        if (govEntity == null) {
            Picklist picklist = new Picklist(
                    AccountingModule.PICKLIST_RISK_DOCUMENT_MANAGEMENT_GOVERNMENT_ENTITY,
                    "Risk Document Management Governement Entity");

            picklist.addItem(new PicklistItem(picklist, "DED", "ded"));
            picklist.addItem(new PicklistItem(picklist, "DM", "dm"));
            picklist.addItem(new PicklistItem(picklist, "GDRFA", "gdrfa"));
            picklist.addItem(new PicklistItem(picklist, "MOHRE", "mohre"));
            picklist.addItem(new PicklistItem(picklist, "rta", "RTA"));
            picklistRepository.save(picklist);
        }

        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('data-migration','handleUpdatesRelatedRejectionFlowAcc8851')")
    @GetMapping(path = "/handleUpdatesRelatedRejectionFlowAcc8851")
    @Transactional
    public ResponseEntity<?> handleUpdatesRelatedRejectionFlowAcc8851(@RequestBody Map<String, Object> body) {

        dataMigrationService.handleUpdatesRelatedRejectionFlowAcc8851();
        dataMigrationService.handleAllContractsPEDInFutureAndIsSFTCausedRejectionFlowAcc8851(body);
        return ResponseEntity.ok("Done");
    }

    @PreAuthorize("hasPermission('data-migration','updateHousemaidTransactionContractIds')")
    @PostMapping("/updateHousemaidTransactionContractIds")
    @Transactional
    public ResponseEntity<?> updateHousemaidTransactionContractIds() {
        PaginationHelper.processPaginated(
                "updateHousemaidTransactionContractIds",
                housemaidTransactionRepository::findTransactionsNeedingContractIdUpdate,
                this::processHousemaidTransaction
        );

        return ResponseEntity.ok("Done");
    }

    private void processHousemaidTransaction(Map<String, Object> m) {
        HousemaidTransaction hmTransaction = (HousemaidTransaction) m.get("housemaidTransaction");
        Date creationDate = (Date) m.get("creationDate");

        HistorySelectQuery<Contract> query = new HistorySelectQuery<>(Contract.class);
        query.filterBy("housemaid", "=", hmTransaction.getHousemaid());
        query.filterByChanged("housemaid");
        query.filterBy("lastModificationDate", "<=", creationDate);
        query.sortBy("lastModificationDate", false);
        query.setLimit(1);

        List<Contract> contracts = query.execute();
        Long contractId = contracts.isEmpty() ? null : contracts.get(0).getId();

        if (contractId != null) {
            hmTransaction.setContractId(contractId);
            housemaidTransactionRepository.save(hmTransaction);
        } else {
            logger.log(Level.WARNING, "No contract found for housemaid {0} at date {1}",
                    new Object[]{hmTransaction.getHousemaid().getId(), creationDate});
        }
    }
}
