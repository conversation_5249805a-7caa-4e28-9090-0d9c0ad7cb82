package com.magnamedia.module;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.GPTTemplatePromptMessage;
import com.magnamedia.core.entity.Picklist;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.helper.chatai.ChatMessageRole;
import com.magnamedia.core.helper.chatai.OpenAiMessageContentType;

import java.util.ArrayList;
import java.util.List;

public class ChatAITemplateService {

    public static void createTemplates() {
        validateExtractedSignatureByGPT();

        extractAmountsFromDdcMessage();

        extractDataFromAnnexureOcrText();
    }

    private static void validateExtractedSignatureByGPT() {

        GPTTemplatePromptMessage msg = new GPTTemplatePromptMessage(
                "Please answer with just 'YES' if this image include valid human signature else please answer with just 'NO'",
                ChatMessageRole.USER);
        msg.setType(OpenAiMessageContentType.text);

        GPTTemplatePromptMessage msg2 = new GPTTemplatePromptMessage("@img@",
                ChatMessageRole.USER);
        msg2.setType(OpenAiMessageContentType.image_file);

        List<GPTTemplatePromptMessage> messages = new ArrayList<>();
        messages.add(msg);
        messages.add(msg2);

        new Template.GPTTemplateBuilder()
                .template("gpt_validate_extracted_signature",
                        "recognize if an attachment contains valid signature",
                        "recognize if an attachment contains valid signature")
                .isConversational()
                .gptTemperature(0.0)
                .gptTopP(0.9)
                .gptModel(Setup.getOrCreateItem(Picklist.GPT_MODELS, "gpt-4-vision-preview"))
                .messages(messages)
                .build();
    }

    private static void extractAmountsFromDdcMessage() {

        GPTTemplatePromptMessage msg = new GPTTemplatePromptMessage(
                "Extract all unique amounts from this image. without including the currency or a thousand separator. " +
                        "Sum the maid salary with the related amount. Sum payments within the same day. " +
                        "List the results as comma-separated values without duplication. If no amounts are found, " +
                        "display \"No_Text\" instead. Do not include any additional text.",
                ChatMessageRole.USER);
        msg.setType(OpenAiMessageContentType.text);

        GPTTemplatePromptMessage msg2 = new GPTTemplatePromptMessage("@img@",
                ChatMessageRole.USER);
        msg2.setType(OpenAiMessageContentType.image_file);

        List<GPTTemplatePromptMessage> messages = new ArrayList<>();
        messages.add(msg);
        messages.add(msg2);

        new Template.GPTTemplateBuilder()
                .template("gpt_extract_amounts_from_ddc_message",
                        "recognize if an attachment contains specific text",
                        "recognize if an attachment contains specific text")
                .isConversational()
                .gptTemperature(0.0)
                .gptTopP(0.0)
                .gptModel(Setup.getOrCreateItem(Picklist.GPT_MODELS, "gpt-4-vision-preview"))
                .messages(messages)
                .build();
    }

    private static void extractDataFromAnnexureOcrText() {

        GPTTemplatePromptMessage msg = new GPTTemplatePromptMessage("Please read the following OCR-extracted text from a PDF file, which contains a " +
                "structured table with patient visit information. Your task is to accurately " +
                "reconstruct this data into a clean, properly formatted CSV text. Each row in " +
                "the CSV should represent a single entry, and the columns should include the " +
                "following fields (if available): No,Visit Date,MRN,Name,Passport No,UID,Type of Service,Amount" +
                " Ensure that:\n" +
                "The table rows are correctly aligned and merged even if the OCR text is broken or spread across lines.\n" +
                "All extracted data is cleaned of any formatting artifacts.\n" +
                "The CSV output is plain text (not a table), ready to be saved as a .csv file.\n" +
                "Begin parsing from the first record and stop at the end of the table (ignore headers, footers, and invoice metadata).",
                ChatMessageRole.USER);
        msg.setType(OpenAiMessageContentType.text);

        GPTTemplatePromptMessage exampleOCR = new GPTTemplatePromptMessage("Here is an example OCR text that you will process:\n" +
                "\n" + "Example OCR text:\n" + "\n" + "OCR Result: \n" +
                "Document Type: UNKNOWN\n" + "--------------------------\n" +
                "Document SubType: null\n" + "--------------------------\n" +
                "Final Result: No specialized extractor for this type yet!\n" + "--------------------------\n" +
                "Text Result: Annexure\n" +
                "MAIDS CC DOMESTIC WORKERS SERVICES\n" + "PO Box: 40398 - Umm Suqeim Street, Al Barsha\n" + "2nd MSA Showroom No.1\n" + "Dubai, United Arab Emirates.\n" +
                "Attn: Karim Moghrabi\n" + "Tax Invoice No.\n" + ":\n" + "*********\n" + "Tax Invoice Date\n" +
                ":\n" + "Due Date\n" + ":\n" + "31-AUG-2024\n" + "30-SEP-2024\n" + "Customer No\n" + ":\n" + "1321067\n" +
                "Page 1 of 2\n" + "Currency\n" + "AED\n" + "No\n" + "Visit Date\n" + "MRN\n" +
                "Name\n" + "1\n" + "08/01/2024\n" + "0390010824 GAYANI GEETHIKA PERERA\n" + "PANNALAGE\n" + "Passport\n" +
                "No\n" + "N10132484 *********\n" + "UID\n" + "Type of\n" + "Amount\n" + "Service\n" + "Standard\n" + "300.00\n" + "2\n" + "3\n" + "4\n" + "08/01/2024\n" +
                "08/01/2024 0396010824 SUREKHA SURESH AGRE\n" + "SURYAKANT NARAYAN MONDE\n" + "08/01/2024 0543010824 SHERIN IMALKA RODRIGO\n" + "BASTHIYAN KORALALAGE\n" +
                "0550010824 OMANA VARAVUKALAYIL\n" + "Y3922965 *********\n" + "Standard\n" + "300.00\n" + "N10661784 *********\n" + "Standard\n" + "300.00\n" +
                "X4081428 *********\n" + "Standard\n" + "300.00\n" + "5\n" + "08/01/2024\n" + "6\n" + "08/01/2024\n" + "0615010824\n" + "0619010824\n" +
                "MADHAVAN MADHAVAN\n" + "LETHAKUMARI VISHAL NANU\n" + "BIJI AYYAPPAN AYYAPPAN\n" + "V6185245 *********\n" + "Standard\n" + "300.00\n" + "X9726819 *********\n" + "Standard\n" + "300.00\n" + "7\n" +
                "08/01/2024 0582010824\n" + "MARY LIZBETH DE LEON FON\n" + "P7134731B 39865136\n" + "Standard\n" + "250.00",
                ChatMessageRole.USER);

        GPTTemplatePromptMessage exampleOutcome = new GPTTemplatePromptMessage("Expected Output when processing the previous OCR text is below:\n" + "\n" +
                "No,Visit Date,MRN,Name,Passport No,UID,Type of Service,Amount\n" +
                "1,08/01/2024,0390010824,GAYANI GEETHIKA PERERA PANNALAGE,N10132484,*********,Standard,300.00\n" +
                "2,08/01/2024,0396010824,SUREKHA SURESH AGRE SURYAKANT NARAYAN MONDE,Y3922965,*********,Standard,300.00\n" +
                "3,08/01/2024,0543010824,SHERIN IMALKA RODRIGO BASTHIYAN KORALALAGE,N10661784,*********,Standard,300.00\n" +
                "4,08/01/2024,0550010824,OMANA VARAVUKALAYIL MADHAVAN MADHAVAN,X4081428,*********,Standard,300.00\n" +
                "5,08/01/2024,0615010824,LETHAKUMARI VISHAL NANU,V6185245,*********,Standard,300.00\n" +
                "6,08/01/2024,0619010824,BIJI AYYAPPAN AYYAPPAN,X9726819,*********,Standard,300.00\n" +
                "7,08/01/2024,0582010824,MARY LIZBETH DE LEON FON,P7134731B,39865136,Standard,250.00",
                ChatMessageRole.USER);

        GPTTemplatePromptMessage msg2 = new GPTTemplatePromptMessage(
                "Now please process the below OCR text:\n @ocr_text@",
                ChatMessageRole.USER);
        msg2.setType(OpenAiMessageContentType.text);

        List<GPTTemplatePromptMessage> messages = new ArrayList<>();
        messages.add(msg);
        messages.add(exampleOCR);
        messages.add(exampleOutcome);
        messages.add(msg2);

        new Template.GPTTemplateBuilder()
                .template("gpt_parse_annexure_ocr_text",
                        "parse ocr text from annexure",
                        "parse ocr text from annexure")
                .isConversational()
                .gptMaxTokens(5000)
                .gptTemperature(0.0)
                .gptTopP(0.0)
                .gptModel(Setup.getOrCreateItem(Picklist.GPT_MODELS, "gpt-4-vision-preview"))
                .messages(messages)
                .build();
    }
}
