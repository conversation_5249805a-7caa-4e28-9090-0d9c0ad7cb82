package com.magnamedia.scheduledjobs;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.PicklistItem;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.*;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.entity.workflow.FlowSubEventConfig;
import com.magnamedia.helper.ContractPaymentTermHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DirectDebitSource;
import com.magnamedia.module.type.DirectDebitStatus;
import com.magnamedia.module.type.DirectDebitType;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import java.util.*;
import java.util.logging.Logger;

/*

 * <AUTHOR>
 * @created 14/03/2024 - 1:13 PM
 * ACC-6936 ACC-7525

 */
public class GenerateDDsForPaymentExpiryFlowJob implements MagnamediaJob {


    private static final Logger logger = Logger.getLogger(GenerateDDsForPaymentExpiryFlowJob.class.getName());
    private FlowEventConfig flowEventConfig;
    private FlowProcessorService flowProcessorService;
    private FlowProcessorEntityRepository flowProcessorEntityRepository;
    private ContractPaymentTermServiceNew contractPaymentTermServiceNew;
    private DirectDebitSignatureService directDebitSignatureService;
    private FlowSubEventConfig flowSubEventConfig;
    private DirectDebitService directDebitService;
    private DirectDebitRepository directDebitRepository;
    private DirectDebitGenerationPlanRepository directDebitGenerationPlanRepository;
    private InterModuleConnector interModuleConnector;
    private CalculateDiscountsWithVatService calculateDiscountsWithVatService;
    private ContractPaymentTermHelper contractPaymentTermHelper;

    @Override
    public void run(Map<String, ?> map) {
        logger.info("Start job");
        flowProcessorService = Setup.getApplicationContext().getBean(FlowProcessorService.class);
        flowProcessorEntityRepository = Setup.getRepository(FlowProcessorEntityRepository.class);
        flowEventConfig = Setup.getRepository(FlowEventConfigRepository.class)
                .findByName(FlowEventConfig.FlowEventName.PAYMENT_EXPIRY_FLOW);
        flowSubEventConfig = Setup.getRepository(FlowSubEventConfigRepository.class)
                .findByNameAndFlowEventConfig(FlowSubEventConfig.FlowSubEventName.GENERATE_DDS_AFTER_PAID_END_DATE,
                        flowEventConfig);
        directDebitService = Setup.getApplicationContext().getBean(DirectDebitService.class);
        directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        directDebitGenerationPlanRepository = Setup.getRepository(DirectDebitGenerationPlanRepository.class);
        interModuleConnector = Setup.getApplicationContext().getBean(InterModuleConnector.class);
        calculateDiscountsWithVatService = Setup.getApplicationContext().getBean(CalculateDiscountsWithVatService.class);
        contractPaymentTermHelper = Setup.getApplicationContext().getBean(ContractPaymentTermHelper.class);

        if (flowEventConfig == null) return;
        logger.info( "flowEventConfig id: " + flowEventConfig.getId());

        createPaymentExpiryFlow();
        paymentExpiryFlowTermination();
        logger.info("End job");
    }

    private void createPaymentExpiryFlow() {

        contractPaymentTermServiceNew = Setup.getApplicationContext().getBean(ContractPaymentTermServiceNew.class);
        directDebitSignatureService = Setup.getApplicationContext().getBean(DirectDebitSignatureService.class);
        ContractService contractService = Setup.getApplicationContext().getBean(ContractService.class);
        PicklistItem monthlyPayment = Setup.getItem("TypeOfPayment", "monthly_payment");

        ContractRepository contractRepository = Setup.getRepository(ContractRepository.class);
        List<DirectDebitStatus> notAllowedStatuses = Arrays.asList(
                DirectDebitStatus.CANCELED,
                DirectDebitStatus.REJECTED,
                DirectDebitStatus.PENDING_FOR_CANCELLATION,
                DirectDebitStatus.EXPIRED);
        Long lastId = -1L;
        Page<Contract> contracts;

        do {
            int xDays = Integer.parseInt(Setup.getParameter(
                    Setup.getCurrentModule(), AccountingModule.PARAMETER_PAYMENT_EXPIRY_FLOW_BEFORE_X_DAYS_PAID_END_DATE_PERIOD));
            contracts = contractRepository.findForPaymentExpiryFlow(
                    lastId,
                    new LocalDate().toDate(),
                    new LocalDate().plusDays(xDays).toDate(),
                    Arrays.asList(FlowEventConfig.FlowEventName.PAYMENT_EXPIRY_FLOW,
                            FlowEventConfig.FlowEventName.CLIENT_PAID_CASH_NO_SIGNATURE_PROVIDED,
                            FlowEventConfig.FlowEventName.EXTENSION_FLOW),
                    PageRequest.of(0, 200));

            contracts.getContent().forEach(contract -> {
                try {
                    logger.info( "contract id : " + contract.getId());

                    // ACC-8662
                    if (contractService.hasPreventCreateOtherDds(contract)) {
                        logger.info("contract has Prevent Create Other Dds -> exiting");
                        return;
                    }

                    if (directDebitRepository.existsMonthlyDdCoverAfterPaidEndDate(contract,
                            new LocalDate(contract.getPaidEndDate())
                                    .plusMonths(1).dayOfMonth().withMinimumValue().toDate(),
                            notAllowedStatuses)) {
                        logger.info("contract has active monthly dd -> exiting");
                        return;
                    }

                    if (directDebitRepository.existsRejectionFlowOfMonthlyDdCoverAfterPaidEndDate(contract,
                            new LocalDate(contract.getPaidEndDate())
                                    .plusMonths(1).dayOfMonth().withMinimumValue().toDate())) {
                        logger.info("contract has rejection flow -> exiting");
                        return;
                    }

                    if (new LocalDate(contract.getStartOfContract()).toString("yyyy-MM-dd")
                            .equals(new LocalDate(contract.getPaidEndDate()).toString("yyyy-MM-dd")) ||
                            directDebitService.contractHasOpenDdcToDo(contract.getId()) ) {
                        logger.info("contract has open DDC -> exiting");
                        return;
                    }

                    // ACC-7525 Contract has at least 1 closed DDC to-do in its log OR created the contract via CC payments
                    // ACC-9309 and if contract is after parameter value
                    Date minContractCreationDate = new LocalDate(Setup.getParameter(Setup.getCurrentModule(),
                            AccountingModule.PARAMETER_DATE_TO_EXCLUDE_CONTRACTS_FROM_PAYMENT_EXPIRY_FLOW)).toDate();

                    if (!interModuleConnector.get("/sales/appsserviceddapprovaltodo/contracthastodo?" +
                            "contract=" + contract.getId() + "&isClosed=true", Boolean.class) &&
                            contract.getCreationDate().after(minContractCreationDate)) {
                        logger.info("Contract has not closed DDC -> exiting");
                        return;
                    }

                    ContractPaymentTerm cpt = contract.getActiveContractPaymentTerm();
                    Map<String, Object> lastSignatureType = directDebitSignatureService
                            .getLastSignatureType(cpt, false, false);

                    boolean useOldSignatures = ((Boolean) lastSignatureType.get("useApprovedSignature") ||
                            (Boolean) lastSignatureType.get("useNonRejectedSignature"));

                    Map<String, Object> additionalInfo = generateDdsForPaymentExpiryFlow(cpt, monthlyPayment, useOldSignatures);

                    startPaymentExpiryFlow(cpt,
                            new HashMap<String, Object>() {{
                                put("additionalInfo", additionalInfo);
                            }},
                            useOldSignatures && directDebitService.isRequiredBankInfoExist(cpt));
                }
                catch (Exception e) {
                    e.printStackTrace();
                }
            });

            if (!contracts.getContent().isEmpty()) {
                lastId = contracts.getContent().get(contracts.getContent().size() - 1).getId();
            }
        }
        while (!contracts.getContent().isEmpty());
    }

    private Map<String, Object> generateDdsForPaymentExpiryFlow(
            ContractPaymentTerm cpt, PicklistItem monthlyPayment, boolean useOldSignatures) {

        Map<String, Object> additionalInfo = new HashMap<>();
        Map<String, Object> params = new HashMap<>();

        //ACC-9058
        params.put("ddSource", DirectDebitSource.PAYMENT_EXPIRY_AUTO_EXTENSION.toString());
        LocalDate ddaStartDate = new LocalDate(cpt.getContract().getPaidEndDate())
                .plusMonths(1).dayOfMonth().withMinimumValue();
        int ontTimeDDMonthDuration = Integer.parseInt(Setup.getParameter(Setup.getCurrentModule(),
                AccountingModule.PARAMETER_ONE_TIME_DD_MONTH_DURATION));
        int ddNum = 0;
        // ACC-9612
        List<DirectDebit> ddList = new ArrayList<>();
        List<DirectDebitGenerationPlan> plans = directDebitGenerationPlanRepository.findPlansForPaymentExpiryFlow(cpt.getContract());

        DirectDebitGenerationPlan plan = null;
        Iterator<DirectDebitGenerationPlan> iterator = plans.iterator();

        if (iterator.hasNext()) {
            plan = iterator.next();
        }

        while (iterator.hasNext()) {
            if (new LocalDate(plan.getDDSendDate()).withDayOfMonth(1)
                    .isBefore(ddaStartDate.withDayOfMonth(1))) {
                plan = iterator.next();
                continue;
            }

            if (new LocalDate(plan.getDDSendDate()).toString("yyyy-MM").equals(
                    new LocalDate(ddaStartDate).toString("yyyy-MM"))) {
                break;
            }
        }

        // handle first dda with plan
        if (plan != null && new LocalDate(plan.getDDSendDate()).toString("yyyy-MM")
                .equals(ddaStartDate.toString("yyyy-MM"))) {
            List<ContractPayment> contractPayments = new ArrayList<>(generateContractPaymentsFromDDGenerationPlan(plan, params));
            params.put("plansPayments", contractPayments);
            if (iterator.hasNext()) {
                plan = iterator.next();
            }
        }

        DirectDebit firstDda = contractPaymentTermServiceNew.addNewDD(
                cpt.getContract(), ddaStartDate.toDate(),
                ddaStartDate.plusMonths(ontTimeDDMonthDuration).toDate(), null, null, null,
                calculateDiscountsWithVatService.getCPTAmountAtTime(cpt, ddaStartDate.toDate()), null,
                DirectDebitType.ONE_TIME, monthlyPayment, useOldSignatures, null,
                true, true, true, null, true, params);
        additionalInfo.put("dda_" + ddNum, firstDda.getId());
        ddList.add(firstDda);
        ddNum++;

        params.remove("plansPayments");

        // handle second dda with plan
        if (plan!= null && new LocalDate(plan.getDDSendDate()).toString("yyyy-MM")
                .equals(ddaStartDate.plusMonths(1).toString("yyyy-MM"))) {
            List<ContractPayment> contractPayments = new ArrayList<>(generateContractPaymentsFromDDGenerationPlan(plan, params));
            params.put("plansPayments", contractPayments);
            if (iterator.hasNext()) {
                plan = iterator.next();
            }
        }

        DirectDebit secondDda = contractPaymentTermServiceNew.addNewDD(
                cpt.getContract(), ddaStartDate.plusMonths(1).toDate(),
                ddaStartDate.plusMonths(1).plusMonths(ontTimeDDMonthDuration).toDate(), null, null, null,
                calculateDiscountsWithVatService.getCPTAmountAtTime(cpt, ddaStartDate.plusMonths(1).toDate()), null,
                DirectDebitType.ONE_TIME, monthlyPayment, useOldSignatures, null,
                true, true, true, null, true, params);
        additionalInfo.put("dda_" + ddNum, secondDda.getId());
        ddList.add(secondDda);
        ddNum++;

        // Handle ddb with plan
        LocalDate ddbStartDate = ddaStartDate.plusMonths(2);
        LocalDate ddbEndDate = ddbStartDate.plusMonths(Setup.getApplicationContext()
                .getBean(SwitchingNationalityService.class).getDefaultPaymentsDuration(cpt.getContract()) - 2);

        while(iterator.hasNext()) {
            if (plan != null && new LocalDate(plan.getDDSendDate()).toString("yyyy-MM")
                    .equals(ddbStartDate.toString("yyyy-MM"))) {

                List<ContractPayment> contractPayments = new ArrayList<>(generateContractPaymentsFromDDGenerationPlan(plan, params));
                params.put("plansPayments", contractPayments);
                DirectDebit dda = contractPaymentTermServiceNew.addNewDD(
                        cpt.getContract(), ddbStartDate.toDate(),
                        ddbStartDate.plusMonths(1).plusMonths(ontTimeDDMonthDuration).toDate(), null, null, null,
                        calculateDiscountsWithVatService.getCPTAmountAtTime(cpt, ddbStartDate.toDate()), null,
                        DirectDebitType.ONE_TIME, monthlyPayment, useOldSignatures, null,
                        true, true, true, null, true, params);
                logger.info("shifted dda id  : " + dda.getId());
                additionalInfo.put("dda_" + ddNum, dda.getId());
                ddList.add(dda);
                params.remove("plansPayments");
                ddNum++;
                ddbStartDate = ddbStartDate.plusMonths(1);

                plan = iterator.next();
            } else {
                break;
            }
        }

        DirectDebit ddb = contractPaymentTermServiceNew.addNewDD(
                cpt.getContract(), ddbStartDate.toDate(), ddbEndDate.toDate(),
                null, null, null, null, null,
                DirectDebitType.MONTHLY, null, useOldSignatures, null,
                false, true, true, null, true, params);
        additionalInfo.put("ddbId", ddb.getId());
        ddList.add(ddb);

        // ACC-8851
        // Remove hidden flag and set DDC form the Cpt
        Long ddcId = cpt.getDdcId();
        if (ContractService.hasBaseAdditionalInfoByKey(cpt.getContract().getId(), Contract.SKIP_DDC_APPROVAL_FOR_EXPIRY, "true")) {
            ddList.forEach(dd -> {
                if (dd.getDdcId() != null || ddcId == null) return;

                dd.setHidden(false);
                dd.setDdcId(ddcId);
                directDebitRepository.save(dd);
            });
            cpt.getContract().addBaseAdditionalInfo(Contract.SKIP_DDC_APPROVAL_FOR_EXPIRY, "false");
        }

        return additionalInfo;
    }

    private List<ContractPayment> generateContractPaymentsFromDDGenerationPlan(DirectDebitGenerationPlan plan, Map<String, Object> map) {
        DateTime startDate = new DateTime(plan.getDDGenerationDate()).dayOfMonth().withMinimumValue();
        DateTime endDate = startDate.plusMonths(1).dayOfMonth().withMinimumValue().withTimeAtStartOfDay().minusMillis(1);
        List<ContractPayment> l = contractPaymentTermServiceNew.generateDDPaymentsBetweenDates(
                startDate, endDate, plan.getAdditionalDiscountAmount(), null,
                plan.getContract().getActiveContractPaymentTerm(),
                plan.getAmount(), DirectDebitType.MONTHLY, null, true, map);

        l.addAll(new ArrayList<>(contractPaymentTermServiceNew.generateDDPaymentsBetweenDates(
                startDate, endDate, plan.getAdditionalDiscountAmount(), null,
                plan.getContract().getActiveContractPaymentTerm(),
                plan.getAmount(), DirectDebitType.ONE_TIME,
                contractPaymentTermHelper.getItem("TypeOfPayment", "same_day_recruitment_fee"), true, map)));

        return l;
    }

    private void startPaymentExpiryFlow(ContractPaymentTerm cpt, Map<String, Object> map, boolean bankInfoAndSignaturesAvailable) {

        logger.info( "start payment expiry flow for cpt id: " + cpt.getId());

        map.put("trials", 0);
        map.put("reminders", 1);
        FlowProcessorEntity f = flowProcessorService.createFlowProcessor(flowEventConfig, flowSubEventConfig, cpt, map);

        if (bankInfoAndSignaturesAvailable) {
            logger.info("bank Info And Signatures Available");
            f.setCompleted(true);
            flowProcessorEntityRepository.save(f);
        }
    }

    private void paymentExpiryFlowTermination() {

        ContractService contractService = Setup.getApplicationContext()
                .getBean(ContractService.class);
        for (FlowProcessorEntity f : flowProcessorEntityRepository
                .findByFlowEventConfig_NameAndStoppedFalseAndCompletedFalse(FlowEventConfig.FlowEventName.PAYMENT_EXPIRY_FLOW)) {
            try {
                logger.info("flow id: " + f.getId());

                if (flowProcessorService.clientProvidesSignatureAndBankInfo(f.getContract(), f.getCreationDate())) {
                    f.setCompleted(true);
                    flowProcessorEntityRepository.save(f);
                    logger.info("Flow stopped because client provided signatures");
                    continue;
                }

                // ACC-9697
                if (f.getContract().isPayingViaCreditCard() ||
                        new LocalDate(f.getContract().getPaidEndDate())
                        .isAfter(new LocalDate(f.getCreationDate()).dayOfMonth().withMaximumValue())) {
                    f.setStopped(true);
                    flowProcessorEntityRepository.save(f);
                    logger.info("Flow stopped because PED was changed to future or the contract flagged as paying via CC");
                    continue;
                }

                if (new DateTime().isAfter(new DateTime(f.getContract().getPaidEndDate()))) {

                    // ACC-8725
                    // Check if the flow related with any Hidden Direct Debit -> Stop Termination of Contract
                    if (f.getRelatedDirectDebits()
                            .stream()
                            .anyMatch(DirectDebit::isHidden)) {

                        logger.info("The Flow related with a Direct Debit Hidden -> exiting");
                        continue;
                    }

                    // Termination of Contract
                    contractService.setContractForTermination(f.getContract(), f.getFlowEventConfig().getCancellationReason().getCode());
                    f.setCausedTermination(true);
                    f.setStopped(true);
                    flowProcessorEntityRepository.save(f);
                    logger.info("Flow stopped because client didn't provide signatures");
                }
            }
            catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}