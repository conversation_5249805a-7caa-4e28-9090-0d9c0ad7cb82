package com.magnamedia.scheduledjobs;


/*
 * <AUTHOR>
 * @created 30/06/2024 - 1:42 PM
 * ACC-7321
 */

import com.magnamedia.controller.ContractPaymentTermController;
import com.magnamedia.core.Setup;
import com.magnamedia.core.imc.InterModuleConnector;
import com.magnamedia.core.repository.AttachementRepository;
import com.magnamedia.core.schedule.MagnamediaJob;
import com.magnamedia.entity.*;
import com.magnamedia.entity.AppsServiceDDApprovalTodo.DdcTodoType;
import com.magnamedia.entity.AppsServiceDDApprovalTodo;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.repository.DirectDebitRepository;
import com.magnamedia.service.*;
import com.magnamedia.module.type.DirectDebitSource;
import com.magnamedia.module.type.DirectDebitCancellationToDoReason;
import com.magnamedia.repository.*;
import com.magnamedia.service.*;
import org.joda.time.LocalDateTime;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

public class DDsHiddenProcessingJob implements MagnamediaJob {

    public static final Logger logger = Logger.getLogger(DDsHiddenProcessingJob.class.getName());


    private final InterModuleConnector moduleConnector;
    private final DirectDebitRepository directDebitRepository;
    private final DirectDebitService directDebitService;
    private final DirectDebitSignatureService directDebitSignatureService;
    private final AppsServiceDDApprovalTodoService appsServiceDDApprovalTodoService;
    private final IncompleteDirectDebitService incompleteDirectDebitService;
    private final AppsServiceDDApprovalTodoRepository appsServiceDDApprovalTodoRepository;

    private final DirectDebitCancellationService directDebitCancellationService;

    private final AfterCashFlowService afterCashFlowService;

    public DDsHiddenProcessingJob() {
        moduleConnector = Setup.getApplicationContext().getBean(InterModuleConnector.class);
        directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        directDebitService = Setup.getApplicationContext().getBean(DirectDebitService.class);
        directDebitSignatureService = Setup.getApplicationContext().getBean(DirectDebitSignatureService.class);
        appsServiceDDApprovalTodoService = Setup.getApplicationContext().getBean(AppsServiceDDApprovalTodoService.class);
        incompleteDirectDebitService = Setup.getApplicationContext().getBean(IncompleteDirectDebitService.class);
        appsServiceDDApprovalTodoRepository = Setup.getApplicationContext().getBean(AppsServiceDDApprovalTodoRepository.class);
        directDebitCancellationService = Setup.getApplicationContext().getBean(DirectDebitCancellationService.class);
        afterCashFlowService = Setup.getApplicationContext().getBean(AfterCashFlowService.class);
    }

    @Override
    public void run(Map<String, ?> parameters) {
        logger.info("Started job");

        processCreateDDC();
        processHiddenDd();
        processHiddenDdWithRejectedDdc();
        unlinkAllDdCanceledWithDDC();

        logger.info("Ended job");
    }

    private void processCreateDDC() {
        logger.info("Started");

        Date date = new LocalDateTime().minusHours(Integer.parseInt(Setup
                .getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_X_HOUR_AGO_DURING_WHICH_ALL_GENERATED_DD_HIDDEN))).toDate();
        Map<Long, List<DirectDebit>> cptList = directDebitRepository
                .findAllDdHiddenGeneratedBeforeCreationDate(date, DirectDebitService.notAllowedStatuses)
                .stream()
                .collect(Collectors.groupingBy(dd -> dd.getContractPaymentTerm().getId()));

        logger.info("cptList size: " + cptList.size());
        if (cptList.isEmpty()) return;

        cptList.forEach((cptId, dds) -> {
            try {
                logger.info("cptId: " + cptId + "; dds size: " + dds.size());
                ContractPaymentTerm cpt = dds.get(0).getContractPaymentTerm();

                // ignore handel dds if contract has open main DDC_todo
                if (directDebitService.contractHasOpenMainDdcToDo(cpt.getContract().getId())) {
                    return;
                }

                // ACC-9058
                if (ContractService.hasBaseAdditionalInfoByKey(cpt.getContract().getId(), Contract.SKIP_DDC_APPROVAL_FOR_EXPIRY, "true")) {
                    return;
                }

                // Link all matched dds with main ddc and Remove Hidden
                if (cpt.getDdcId() != null) {
                    dds.stream().filter(d -> !DirectDebitSource.PAYMENT_EXPIRY_AUTO_EXTENSION.equals(d.getSource()))
                    .forEach(dd -> {
                        if (directDebitService.checkActiveCPTAndIfPaymentMatchedWithPTC(dd, null)) {
                            logger.info("direct Debit id: " + dd.getId() + " is Matched with Main ddc id");
                            dd.setDdcId(cpt.getDdcId());
                            if (appsServiceDDApprovalTodoService.existsClosedDdcWithConfirmation(cpt.getDdcId())) {
                                dd.setHidden(false);
                                AppsServiceDDApprovalTodo ddcTodo = appsServiceDDApprovalTodoRepository.findOne(cpt.getDdcId());
                                if (ddcTodo != null && ddcTodo.getDdDataEntryNote() != null && dd.getDataEntryNotes() == null) {
                                    dd.setDataEntryNotes(ddcTodo.getDdDataEntryNote());
                                }
                            }
                            directDebitRepository.silentSave(dd);
                        }
                    });

                    //  validate and start missing bank info flow if have not one and dd status IN_COMPLETE
                    incompleteDirectDebitService.validateAndStartIncompleteFlowMissingBankInfo(dds, cpt);
                }

                // remove all dds matched with main ddc Then check if all dds is matched => return
                dds.removeIf(dd -> !dd.isHidden());

                if (dds.isEmpty()) {
                    return;
                }

                // create new DDC
                directDebitSignatureService.createNewDdcTodo(cpt,
                                cpt.getDdcId() == null ? DdcTodoType.GET_CONFIRMATION : DdcTodoType.APPROVAL_TODO,
                                dds);
            } catch (Exception e) {
               e.printStackTrace();
            }
        });
    }

    private void processHiddenDd() {
        logger.info("Started");
        Map<Long, List<DirectDebit>> ddsByDdc = directDebitRepository.findAllDdHiddenRelatedWithClosedDdc()
                .stream()
                .collect(Collectors.groupingBy(DirectDebit::getDdcId));

        logger.info("ddsByDdc size: " + ddsByDdc.size());
        if (ddsByDdc.isEmpty()) return;

        ddsByDdc.forEach((ddcId, dds) -> {
            try {
                logger.info("ddcId: " + ddcId + "; dds size: " + dds.size());

                dds.forEach(dd -> {
                    // remove hidden dd when ddc status is (closed or approved)
                    dd.setHidden(false);
                    AppsServiceDDApprovalTodo ddcTodo = appsServiceDDApprovalTodoRepository.findOne(dd.getDdcId());
                    if (ddcTodo != null && ddcTodo.getDdDataEntryNote() != null && dd.getDataEntryNotes() == null) {
                        dd.setDataEntryNotes(ddcTodo.getDdDataEntryNote());
                    }
                    directDebitRepository.silentSave(dd);
                });

                // after close DDC run missing bank info flow if have not one and dd status IN_COMPLETE
                incompleteDirectDebitService.validateAndStartIncompleteFlowMissingBankInfo(dds, dds.get(0).getContractPaymentTerm());
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    // ACC-9426
    private void processHiddenDdWithRejectedDdc() {
        logger.info("Started");
        Map<Long, List<DirectDebit>> ddsByDdc = directDebitRepository.findAllDdHiddenRelatedWithRejectedDdcForIpam()
                .stream()
                .collect(Collectors.groupingBy(DirectDebit::getDdcId));

        logger.info("ddsByDdc size: " + ddsByDdc.size());
        if (ddsByDdc.isEmpty()) return;

        ddsByDdc.forEach((ddcId, dds) -> {
            try {
                logger.info("ddcId: " + ddcId + "; dds size: " + dds.size());
                ContractPaymentTerm cpt = dds.get(0).getContractPaymentTerm();

                removeBankInfo(cpt);

                //Cancel DDs
                for (DirectDebit dd : dds) {
                    directDebitCancellationService.cancelWholeDD(dd, false,
                            DirectDebitCancellationToDoReason.CLIENT_PAID_CASH_EXTENDING_AFTER_CASH_FLOW, false);
                }

                // Reactivate the IPAM flow for this term
                afterCashFlowService.reactivateFlow(cpt,true);

                // Unlink the DDC To-Do from the term
                cpt.setDdcId(null);
                Setup.getRepository(ContractPaymentTermRepository.class).silentSave(cpt);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    private void removeBankInfo(ContractPaymentTerm cpt) {

//        Contract contract = cpt.getContract();
//        List<String> tags = Arrays.asList(
//                Contract.TEMP_EID_ATTACHMENT_TAG,
//                Contract.TEMP_IBAN_ATTACHMENT_TAG,
//                Contract.TEMP_ACCOUNT_NAME_ATTACHMENT_TAG
//        );
//
//        for (String tag : tags) {
//            if (contract.deleteAttachmentByTag(tag)) {
//                logger.info("[Contract " + contract.getId()
//                        + "] Deleted attachment tag: " + tag);
//            }
//        }
//        Setup.getRepository(ContractRepository.class).save(contract);

        // Remove permanent bank info attachments from CPT
        List<String> permanentBankInfoTags = Arrays.asList(
                ContractPaymentTermController.FILE_TAG_BANK_INFO_EID,
                ContractPaymentTermController.FILE_TAG_BANK_INFO_IBAN,
                ContractPaymentTermController.FILE_TAG_BANK_INFO_ACCOUNT_NAME);

        AttachementRepository attachementRepository = Setup.getRepository(AttachementRepository.class);
        cpt.getAttachments().stream()
                .filter(att -> permanentBankInfoTags.contains(att.getTag()))
                .forEach(att -> {
                    attachementRepository.delete(att);
                    logger.info("[CPT " + cpt.getId() + "] Deleted attachment tag: " + att.getTag());
                });

        if (cpt.getIbanNumber() != null || cpt.getEid() != null || cpt.getAccountName() != null) {
            // Wipe the bank-info fields on the current term
            cpt.setIbanNumber(null);
            cpt.setEid(null);
            cpt.setAccountName(null);
            logger.info("[CPT " + cpt.getId() + "] Cleared iban / eid / accountName");
        }
    }


    public void unlinkAllDdCanceledWithDDC() {
        logger.info("Started unlinkAllDdCanceledWithDDC");
        List<DirectDebit> dds = directDebitRepository.findAllDdCanceledAndHiddenRelatedWithDdc(DirectDebitService.notAllowedStatusesWithRejected);

        logger.info("dds Size: " + dds.size());
        if (dds.isEmpty()) return;

        Set<Long> ddcIds = new HashSet<>();
        dds.forEach(dd -> {
            try {
                logger.info("unlink dd id: " + dd.getDdcId());
                if (!ddcIds.contains(dd.getDdcId()) &&
                        !directDebitRepository.existsOtherActiveDdsRelatedToSameDdc(
                                dd.getId(), dd.getDdcId(), DirectDebitService.notAllowedStatusesWithRejected)) {
                    logger.info("close this ddc after canceled all dd, ddcId: " + dd.getDdcId());
                    ddcIds.add(dd.getDdcId());

                    // Call api close DDC
                    String url = "/sales/appsserviceddapprovaltodo/closeapprovaltodo?todo=" + dd.getDdcId();
                    logger.info("Close the DDC todo -> URL: " + url);
                    moduleConnector.postJsonAsync(url, new HashMap<>());
                }

                // when canceled DD remove link between dd with ddc
                dd.setDdcId(null);
                directDebitRepository.silentSave(dd);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }
}