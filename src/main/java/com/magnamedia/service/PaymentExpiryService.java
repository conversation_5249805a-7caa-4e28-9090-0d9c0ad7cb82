package com.magnamedia.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Template;
import com.magnamedia.core.helper.TemplateUtil;
import com.magnamedia.core.notification.AppAction;
import com.magnamedia.core.type.AppActionType;
import com.magnamedia.core.type.CoreParameter;
import com.magnamedia.core.type.FunctionType;
import com.magnamedia.core.type.NavigationType;
import com.magnamedia.entity.Client;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.ContractPaymentTerm;
import com.magnamedia.entity.DDMessagingToDo;
import com.magnamedia.extra.CcNotificationTemplateCode;
import com.magnamedia.extra.MvNotificationTemplateCode;
import com.magnamedia.extra.Utils;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.DDMessagingType;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.DDMessagingToDoRepository;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Time;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

@Service
public class PaymentExpiryService {
    private static final Logger logger = Logger.getLogger(PaymentExpiryService.class.getName());

    @Autowired
    MessagingService messagingService;
    @Autowired
    FlowProcessorMessagingService flowProcessorMessagingService;
    @Autowired
    ContractRepository contractRepository;

    public void SendFirstMessage(ContractPaymentTerm cpt) throws JsonProcessingException {
        Contract contract = cpt.getContract();
        logger.info("Payment expiry start send sms clientId: " + contract.getClient().getId());

        Map<String, String> parameters = new HashMap<>();
        Map<String, AppAction> cta = new HashMap<>();
        flowProcessorMessagingService.fillExpiryPaymentParameter(cpt, parameters);
        flowProcessorMessagingService.fillExpiryPaymentContext(cpt, parameters, cta);

        String contentTemplate = contract.isMaidCc()
            ? CcNotificationTemplateCode.CC_PAYMENT_EXPIRY_4_1_1_NOTIFICATION.toString()
            : MvNotificationTemplateCode.MV_PAYMENT_EXPIRY_4_1_1_NOTIFICATION.toString();

        messagingService.sendMessageToClient(contract,
                parameters,
                cta,
                contract.getId(),
                contract.getEntityType(),
                TemplateUtil.getTemplate(contentTemplate));
        }

    public void SendSecondSMS(
            Contract contract,
            Double monthly_fee_of_nationality,
            String ExpiryDate) throws JsonProcessingException {

        logger.info("Payment expiry start send second message for client: " + contract.getClient().getId());

        DDMessagingToDoRepository ddMessagingToDoRepository = Setup.getRepository(DDMessagingToDoRepository.class);
        Client client = contract.getClient();

        Map<String, Object> signDDMap = new HashMap<>();
        signDDMap.put("cpt", contract.getActiveContractPaymentTerm());
        signDDMap.put("additionalInfo", new HashMap<String, Object>() {{
            put("sentFrom", "PaymentExpiryFlow");
        }});
        String signLink = Setup.getApplicationContext().getBean(Utils.class)
                .getSingDDLink(signDDMap);

        Map<String, String> parameters = new HashMap<>();
        parameters.put("ExpiryDate", ExpiryDate);
        parameters.put("link_send_dd_details", signLink);
        parameters.put("monthly_fee_of_nationality", String.valueOf(monthly_fee_of_nationality.intValue()));
        parameters.put("greetings", contract.isMaidCc() ?
                Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSCC) :
                Setup.getCoreParameter(CoreParameter.SMS_GREETINGS_MAIDSVISA));
        parameters.put("link_send_dd_details_click_here", "@link6@");

        Map<String, AppAction> cta = new HashMap<>();
        AppAction a = new AppAction();
        a.setText("click here");
        a.setType(AppActionType.LINK);
        a.setFunctionType(FunctionType.WEB_SERVICE);
        a.setNavigationType(NavigationType.WEB);
        a.setHyperlink(signLink);
        a.setAppRouteName("");
        a.setAppRouteArguments(new HashMap<String, String>(){{
            put("contractId", contract.getId().toString());
            put("contractUuid", contract.getUuid());
        }});
        cta.put("link6", a);

        Template notificationTemplate = TemplateUtil.getTemplate(contract.isMaidCc() ?
                CcNotificationTemplateCode.CC_PAYMENT_EXPIRY_4_1_2_NOTIFICATION.toString() :
                MvNotificationTemplateCode.MV_PAYMENT_EXPIRY_4_1_2_NOTIFICATION.toString());

        messagingService.sendMessageToClient(contract,
                parameters,
                cta,
                contract.getId(),
                contract.getEntityType(),
            notificationTemplate);

        for (int index = 1; index <= 3; index++) {
            java.sql.Date SendDate = java.sql.Date.valueOf(new LocalDate().plusDays(3 * index).toString("yyyy-MM-dd"));
            DDMessagingToDo dDMessagingToDo = new DDMessagingToDo();
            dDMessagingToDo.setAccountName(client.getAccountName());
            dDMessagingToDo.setSendToClient(true);
            dDMessagingToDo.setClientId(client.getId());
            dDMessagingToDo.setType(DDMessagingService.DdMessagingMethod.FORCE_SMS.toString());
            dDMessagingToDo.setEvent(DDMessagingType.ExpiryPayment);
            dDMessagingToDo.setClientName(client.getName());
            dDMessagingToDo.setClientPhoneNumber(client.getNormalizedMobileNumber());
            dDMessagingToDo.setContractUuid(contract.getUuid());
            dDMessagingToDo.setSendTime(Time.valueOf(LocalTime.now()));
            dDMessagingToDo.setSendDate(SendDate);
            dDMessagingToDo.setClientTemplateName(notificationTemplate.getName());
            ddMessagingToDoRepository.save(dDMessagingToDo);
        }
    }

    @Transactional
    public void excludeClientFromExpiryFlow(Contract c, boolean exclude) {
        Contract contract = contractRepository.findOne(c.getId());
        if (contract.isExcludeFromExpiryFlow() == exclude) return;
        contract.setExcludeFromExpiryFlow(exclude);
        contractRepository.silentSave(contract);
    }

    public static boolean isDuringTheExpiryFlowPeriod(Contract c) {
        LocalDate paidEndDate = new LocalDate(c.getPaidEndDate());
        int xDays = Integer.parseInt(Setup.getParameter(
                Setup.getCurrentModule(),
                AccountingModule.PARAMETER_PAYMENT_EXPIRY_FLOW_BEFORE_X_DAYS_PAID_END_DATE_PERIOD));
        boolean includeNextMonthDda = (new LocalDate().isAfter(paidEndDate.minusDays(xDays + 1)) &&
                new LocalDate().isBefore(paidEndDate.plusDays(1))) ||
                (new LocalDate().isAfter(new LocalDate().dayOfMonth().withMaximumValue().minusDays(xDays + 1)) &&
                        new LocalDate().isBefore(new LocalDate().dayOfMonth().withMaximumValue().plusDays(1)));

        logger.info("includeNextMonthDda: " + includeNextMonthDda);
        return includeNextMonthDda;
    }
}