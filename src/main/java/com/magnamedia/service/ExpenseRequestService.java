package com.magnamedia.service;

import com.magnamedia.entity.workflow.ExpensePayment;
import com.magnamedia.entity.workflow.ExpenseRequestTodo;
import com.magnamedia.entity.Transaction;
import com.magnamedia.entity.CreditCardReconciliationStatementDetails;
import com.magnamedia.repository.ExpensePaymentRepository;
import com.magnamedia.repository.ExpenseRequestTodoRepository;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.module.type.ExpensePaymentStatus;
import com.magnamedia.workflow.type.AttachmentTag;
import com.magnamedia.helper.AttachmentHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * Utility service for handling expense confirmation triggers
 * This service contains common logic that can be reused across different controllers
 */
@Service
public class ExpenseRequestService {

    @Autowired
    private ExpenseRequestTodoRepository expenseRequestTodoRepository;

    @Autowired
    private ExpensePaymentRepository expensePaymentRepository;

    /**
     * Handles the after confirmation trigger logic for expense requests
     * This method updates the expense request todo and associated payment with transaction details
     * 
     * @param expenseRequestTodo The expense request todo to update
     * @param matchType The type of match (EXISTING_EXPENSE, AUTO_DEDUCTED, REFUND)
     * @param transaction The transaction that was created
     */
    @Transactional
    public void afterConfirmationTrigger(ExpenseRequestTodo expenseRequestTodo, 
                                       CreditCardReconciliationStatementDetails.MatchType matchType, 
                                       Transaction transaction) {
        if (expenseRequestTodo != null) {
            expenseRequestTodo.setAmountInLocalCurrency(transaction.getAmount());
            expenseRequestTodo.setVatAmount(transaction.getVatAmount());
            
            if (matchType.equals(CreditCardReconciliationStatementDetails.MatchType.REFUND)) {
                expenseRequestTodo.setRefundConfirmed(true);
            }

            if (Arrays.asList(CreditCardReconciliationStatementDetails.MatchType.EXISTING_EXPENSE, 
                            CreditCardReconciliationStatementDetails.MatchType.AUTO_DEDUCTED).contains(matchType)) {
                expenseRequestTodo.setConfirmed(true);
            }

            ExpensePayment expensePayment = expenseRequestTodo.getExpensePayment();
            if (expensePayment != null) {
                expensePayment.setCallUpdateAmountsTrigger(Boolean.FALSE);
                expensePayment.setLocalCurrencyAmount(expenseRequestTodo.getAmountInLocalCurrency());
                expensePayment.setVatAmount(transaction.getVatAmount());
                expensePayment.setTransaction(transaction);
                expensePayment.setConfirmed(true);
                expensePaymentRepository.save(expensePayment);
            }

            expenseRequestTodoRepository.save(expenseRequestTodo);
        }
    }

    /**
     * Handles the reconciliator confirmation step logic for expense payments
     * This method centralizes the reconciliation logic that was previously in ExpensePaymentInReconciliatorConfirmationStep
     * 
     * @param expensePayment The expense payment to reconcile
     */
    @Transactional
    public void reconciliatorConfirmationStep(ExpensePayment expensePayment) {
        expensePayment.setCompleted(Boolean.TRUE);
        
        // Check if invoice is missing to determine status
        if (isInvoiceMissingOrTaxInvoiceMissing(expensePayment)) {
            expensePayment.setStatus(ExpensePaymentStatus.PAID_PENDING_INVOICE);
        } else {
            expensePayment.setStatus(ExpensePaymentStatus.PAID);
        }
        
        expensePayment.setConfirmed(Boolean.TRUE);
        expensePayment.setMissingVatInvoice(isMissingVatInvoice(expensePayment));

        confirmRequests(expensePayment);
    }

    /**
     * Checks if invoice or tax invoice is missing for the expense payment
     * This method replicates the logic from ExpensePaymentInPendingPaymentCashierStep.isInvoiceMessingOrTaxInvoiceMessing
     * 
     * @param expensePayment The expense payment to check
     * @return true if invoice or tax invoice is missing, false otherwise
     */
    public boolean isInvoiceMissingOrTaxInvoiceMissing(ExpensePayment expensePayment) {
        if (expensePayment.getRequiresInvoice() == null) return false;
        if (expensePayment.getRequiresInvoice().equals(Boolean.FALSE)) return false;

        Attachment invoice = AttachmentHelper.getRequestAttachment(expensePayment, AttachmentTag.EXPENSE_PAYMENT_INVOICE.toString());
        if (invoice == null) return true;

        if (expensePayment.getTaxable() == null) return true;
        if (expensePayment.getTaxable().equals(Boolean.FALSE)) return false;

        if (expensePayment.getVatAmount() == null) return true;
        if (expensePayment.getAttachedValidVatInvoice() == null) return true;
        if (expensePayment.getAttachedValidVatInvoice().equals(Boolean.TRUE)) return false;

        Attachment vatInvoice = AttachmentHelper.getRequestAttachment(expensePayment, AttachmentTag.EXPENSE_PAYMENT_VAT_INVOICE.toString());
        if (vatInvoice == null) return true;

        return false;
    }

    /**
     * Checks if VAT invoice is missing for the expense payment
     * 
     * @param expensePayment The expense payment to check
     * @return true if VAT invoice is missing, false otherwise
     */
    public boolean isMissingVatInvoice(ExpensePayment expensePayment) {
        boolean missingVatInvoice = false;
        if (expensePayment.getTaxable() == null) {
            missingVatInvoice = true;
        } else if (expensePayment.getTaxable().equals(Boolean.TRUE)) {
            if (expensePayment.getVatAmount() == null || expensePayment.getVatAmount().equals(0D)) {
                missingVatInvoice = true;
            } else {
                Attachment vatInvoice = expensePayment.getAttachment(AttachmentTag.EXPENSE_PAYMENT_VAT_INVOICE.toString());
                if (vatInvoice == null) {
                    missingVatInvoice = true;
                }
            }
        }
        return missingVatInvoice;
    }

    /**
     * Confirms all expense request todos associated with the expense payment
     * 
     * @param expensePayment The expense payment
     */
    public void confirmRequests(ExpensePayment expensePayment) {
        List<ExpenseRequestTodo> todos = expenseRequestTodoRepository.findByExpensePayment(expensePayment);
        if (todos != null && !todos.isEmpty()) {
            for (ExpenseRequestTodo todo : todos) {
                todo.setConfirmed(Boolean.TRUE);
                todo.setAmount(expensePayment.getAmount());
                todo.setLoanAmount(expensePayment.getLoanAmount());
                expenseRequestTodoRepository.save(todo);
            }
        }
    }
} 