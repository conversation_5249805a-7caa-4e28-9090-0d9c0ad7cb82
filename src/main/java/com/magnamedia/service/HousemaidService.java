package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.BaseAdditionalInfo;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.HistorySelectQuery;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.entity.Contract;
import com.magnamedia.entity.Housemaid;
import com.magnamedia.entity.NewRequest;
import com.magnamedia.extra.AccountingPage;
import com.magnamedia.repository.ContractRepository;
import com.magnamedia.repository.NewVisaRequestRepository;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * Created on Dec 28 , 2024
 */
@Service
public class HousemaidService {

    private static final Logger logger = Logger.getLogger(HousemaidService.class.getName());

    @Autowired
    private NewVisaRequestRepository newVisaRequestRepository;

    @Autowired
    private QueryService queryService;

    public static boolean hasBaseAdditionalInfoByKey(Long housemaidId, String key, String value) {
        return QueryService.existsEntity(BaseAdditionalInfo.class,
                "e.ownerId = :p0 and e.ownerType = 'Housemaid' and e.infoKey = :p1 and e.infoValue = :p2",
                new Object[]{ housemaidId, key, value });
    }

    public List<Housemaid> getHousemaids(
            String mobileNumber, String eidNumber, String firstName,
            String middleName, String lastName, Long contractId) {

        validateParameters(contractId, mobileNumber, eidNumber, firstName, middleName, lastName);
        validateNameFiltration(firstName, middleName, lastName);
        return fetchHousemaids(mobileNumber, eidNumber, firstName, middleName, lastName, contractId);
    }

    private List<Housemaid> fetchHousemaids(
            String mobileNumber, String eidNumber, String firstName,
            String middleName, String lastName, Long contractId) {

        List<Housemaid> housemaids = new ArrayList<>();

        if (StringUtils.isNotBlank(mobileNumber)) {
            housemaids = findMaidByMobileNumber(mobileNumber);
        }

        if (housemaids.isEmpty() && StringUtils.isNotBlank(eidNumber)) {
            housemaids = findHousemaidByEid(eidNumber);
        }

        if (housemaids.isEmpty() && !StringUtils.isAllBlank(firstName, middleName, lastName)) {
            housemaids = findByName(firstName, middleName, lastName);
        }

        if (housemaids.isEmpty() && contractId != null) {
            Contract contract = Setup.getRepository(ContractRepository.class).findOne(contractId);
            if (contract != null && contract.getHousemaid() != null) {
                housemaids.add(contract.getHousemaid());
            }
        }

        return housemaids;
    }

    public List<Housemaid> findMaidByMobileNumber(String mobileNumber) {
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        String normalizedPhoneNumber = com.magnamedia.extra.StringUtils.NormalizePhoneNumber(mobileNumber);
        query.filterBy("phoneNumber", "=", mobileNumber)
                .or("whatsAppPhoneNumber", "=", mobileNumber)
                .or("normalizedPhoneNumber", "=", normalizedPhoneNumber)
                .or("normalizedWhatsAppPhoneNumber", "=", normalizedPhoneNumber);

        query.sortBy("creationDate", false);
        return query.execute();
    }

    public List<Housemaid> findHousemaidByEid(String eidNumber) {
        List<NewRequest> newRequests = newVisaRequestRepository.findVisaNewRequestByNewEidNumberOrderByCreationDate(eidNumber);

        return newRequests.isEmpty() || newRequests.get(0).getHousemaid() == null ? new ArrayList<>()
                : Collections.singletonList(newRequests.get(0).getHousemaid());
    }

    public List<Housemaid> findByName(String firstName, String middleName, String lastName) {
        SelectQuery<Housemaid> query = new SelectQuery<>(Housemaid.class);
        query.filterBy("name", "like", Stream.of(firstName, middleName, lastName)
                .filter(StringUtils::isNotBlank).collect(Collectors.joining("%")) + "%");
        query.sortBy("creationDate", false);
        return query.execute();
    }

    private void validateParameters(Long contractId, String mobileNumber, String eidNumber, String firstName, String middleName, String lastName) {
        if (contractId == null && StringUtils.isAllBlank(mobileNumber, eidNumber, firstName, middleName, lastName)) {
            throw new BusinessException("At least one of the parameters must be provided.");
        }
    }

    private void validateNameFiltration(String firstName, String middleName, String lastName) {
        if (StringUtils.isBlank(firstName) &&
                (StringUtils.isBlank(firstName) && !StringUtils.isAllBlank(middleName, lastName)) ||
                (StringUtils.isBlank(lastName) && StringUtils.isNotBlank(middleName))) {
            throw new BusinessException("Name based filtering should be passed as combination like either (firstName), (firstName, lastName) or (firstName, middleName, lastName).");
        }
    }

    public Date getHousemaidAssignmentDate(Contract contract) {
        if (contract.getHousemaid() == null) {
            return contract.getStartOfContract();
        }

        HistorySelectQuery<Contract> query = new HistorySelectQuery<>(Contract.class);
        query.filterBy("id", "=", contract.getId());
        query.filterBy("housemaid", "=", contract.getHousemaid());
        query.sortBy("lastModificationDate", true); // Most recent first
        query.setLimit(1);

        List<Contract> history = query.execute();
        if (!history.isEmpty()) {
            return history.get(0).getLastModificationDate();
        }

        return contract.getStartOfContract();
    }

    public Map<String, Object> getHousemaidExpenseRequests(
            Long housemaidId, Long contractId, Date fromDate, Date toDate, boolean onlyAmount, Pageable pageable) {

        String fromStatement = " from ExpenseRequestTodo e " +
                "inner join e.expense ex " +
                "left join e.expensePayment ep";

        String query = "select new map(" +
                "e.id as id, " +
                "ex.id as expenseId, " +
                "ex.name as expenseName, " +
                "ex.caption as expenseCaption, " +
                "(select pi1.name from PicklistItem pi1 where pi1 = e.purposeAdditionalDescription) as purposeAdditionalDescription, " +
                "e.creationDate as creationDate, " +
                "e.amount as amount, " +
                "e.loanAmount as loanAmount, " +
                "(select pi1.name from PicklistItem pi1 where pi1 = e.currency) as currency, " +
                "e.paymentMethod as paymentMethod, " +
                "e.description as description, " +
                "e.status as status, " +
                "e.rejectionNotes as rejectionNotes, " +
                "(select u.fullName from User u where u = e.requestedBy) as requestedBy, " +
                "e.approvedBy as approvedBy, " +
                "e.notes as notes, " +
                "ep.id as paymentId," +
                "e.contractId as contractId) ";

        String conditions = " where e.relatedToId = :housemaidId and e.relatedToType = 'MAID' and ep.transaction is null ";
        conditions += " and e.status != 'DISMISSED' ";

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("housemaidId", housemaidId);

        if (fromDate != null) {
            conditions += " and e.creationDate >= :fromDate ";
            parameters.put("fromDate", fromDate);
        }

        Map<String, Object> result = new HashMap<>();

        if (onlyAmount) {
            result.put("totalAmount", (Double) new SelectQuery<>("select sum(e.amountInLocalCurrency) " +
                    fromStatement + conditions,"",
                    Double.class, parameters).execute().get(0));
            return result;
        }

        if (contractId != null) {
            conditions += " and e.contractId = :contractId ";
            parameters.put("contractId", contractId);
        }

        if (toDate != null) {
            conditions += " and e.creationDate <= :toDate ";
            parameters.put("toDate", toDate);
        }

        query += fromStatement + conditions;

        query = queryService.sortDirectQueryByPageable(pageable, query, "e");

        String countQuery = "select count(e.id) " + fromStatement + conditions;

        SelectQuery<Map> q = new SelectQuery<>(query, countQuery, Map.class, parameters);

        Page<Map> p = q.execute(pageable);

        // fill attachments
        List<Map> attachments = queryService.getAttachmentsWithBasicInfo(p.getContent().stream()
                .map(e -> (Long) e.get("id")).collect(Collectors.toList()), "ExpenseRequestTodo");
        p.getContent().forEach(e -> {
            List<Map> attachmentList = attachments.stream()
                    .filter(a -> e.get("id").equals(a.get("ownerId"))).collect(Collectors.toList());
            attachmentList.forEach(a -> a.remove("ownerId"));
            e.put("attachments", attachmentList);
        });

        result.put("data", new AccountingPage(p.getContent(), pageable, p.getTotalElements(), (Double) new SelectQuery<>("select sum(e.amountInLocalCurrency) " +
                fromStatement + conditions,"",
                Double.class, parameters).execute().get(0)));

        return result;
    }

    public Double getHousemaidExpenseRequestsTotalAmount(Long housemaidId, Date fromDate) {
        Map<String, Object> result = getHousemaidExpenseRequests(
                housemaidId, null, fromDate, null, true, null);
        return result.containsKey("totalAmount") ? (Double) result.get("totalAmount") : 0.0;
    }
}
