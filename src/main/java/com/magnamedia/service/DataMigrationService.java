package com.magnamedia.service;

import com.magnamedia.core.Setup;
import com.magnamedia.core.entity.Attachment;
import com.magnamedia.core.exception.BusinessException;
import com.magnamedia.core.helper.SelectQuery;
import com.magnamedia.core.mail.Recipient;
import com.magnamedia.core.mail.TextEmail;
import com.magnamedia.core.type.EmailReceiverType;
import com.magnamedia.entity.*;
import com.magnamedia.entity.projection.DirectDebitGenerationPlanPendingPayingViaCcReportCSVProjection;
import com.magnamedia.entity.workflow.DirectDebitRejectionToDo;
import com.magnamedia.entity.workflow.FlowEventConfig;
import com.magnamedia.extra.ContractScheduleForTerminationUtils;
import com.magnamedia.extra.DirectDebitGenerationPlanPendingPayingViaCcReportCSV;
import com.magnamedia.helper.CsvHelper;
import com.magnamedia.module.AccountingModule;
import com.magnamedia.module.type.*;
import com.magnamedia.repository.*;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.text.NumberFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static com.magnamedia.controller.ContractPaymentTermController.FILE_TAG_PAYMENTS_RECEIPT;

/**
 * <AUTHOR> Mahfoud
 */
@Service
public class DataMigrationService {
    private static final Logger logger = Logger.getLogger(DataMigrationService.class.getName());

    @Autowired
    private PaymentTermConfigRepository paymentTermConfigRepository;
    @Autowired
    private ContractRepository contractRepository;
    @Autowired
    private ContractPaymentTermRepository contractPaymentTermRepository;
    @Autowired
    private HousemaidRepository housemaidRepository;
    @Autowired
    private ReplacementRepository replacementRepository;
    @Autowired
    private DirectDebitRepository directDebitRepository;
    @Autowired
    private ContractPaymentRepository contractPaymentRepository;
    @Autowired
    private DirectDebitSignatureRepository directDebitSignatureRepository;

    // ACC-7890
    public String handleSheetNewCPTs(
            MultipartFile file,
            boolean changeHousemaid, boolean changeReplacement,
            boolean changeReason, boolean changeFirstPayment)
            throws IOException {

        XSSFWorkbook workbook = new XSSFWorkbook(file.getInputStream());
        XSSFSheet sheet = workbook.getSheetAt(0);
        if (sheet == null) throw new BusinessException("sheet is null");

        logger.log(Level.SEVERE, "Sheet Name = {0}", sheet.getSheetName());
        logger.log(Level.SEVERE, "last row number = {0}", sheet.getLastRowNum());

        int contractIdIndex = -1;
        int termIdIndex = -1;
        int housemaidIdIndex = -1;
        int replacementIdIndex = -1;
        int reasonIndex = -1;
        int firstMonthPaymentIndex = -1;

        DataFormatter formatter = new DataFormatter();
        NumberFormat nf_in = NumberFormat.getNumberInstance(Locale.ENGLISH);
        StringBuilder stringBuilder = new StringBuilder();

        String error = "";
        Row firstRow = sheet.getRow(0);

        for (Cell cell : firstRow) {
            switch (cell.getStringCellValue().trim().toLowerCase()) {
                case "contract_id":
                    contractIdIndex = cell.getColumnIndex();
                    break;
                case "term_id":
                    termIdIndex = cell.getColumnIndex();
                    break;
                case "housemaid_id":
                    housemaidIdIndex = cell.getColumnIndex();
                    break;
                case "replacement_id":
                    replacementIdIndex = cell.getColumnIndex();
                    break;
                case "reason":
                    reasonIndex = cell.getColumnIndex();
                    break;
                case "first_month_payment":
                    firstMonthPaymentIndex = cell.getColumnIndex();
                    break;
            }
        }

        if (contractIdIndex == -1)
            error += "CONTRACT_ID column not found ";
        if (termIdIndex == -1)
            error += "TERM_ID column not found ";
        if (changeHousemaid && housemaidIdIndex == -1)
            error += "changeHousemaid is true and HOUSEMAID_ID column not found ";
        if (changeReplacement && replacementIdIndex == -1)
            error += "changeReplacement is true and REPLACEMENT_ID column not found ";
        if (changeReason && reasonIndex == -1)
            error += "changeReason is true and REASON column not found ";
        if (changeFirstPayment && firstMonthPaymentIndex == -1)
            error += "changeFirstPayment is true and FIRST_MONTH_PAYMENT column not found ";

        for(Row row : sheet) {
            int rowNumber = row.getRowNum();

            if (rowNumber == 0) {
                if(!error.isEmpty()) {
                    stringBuilder.append("Error in line: ").append(rowNumber).append(", Error: ").append(error);
                    throw new BusinessException(stringBuilder.toString());
                }
                continue;
            }

            try {
                logger.log(Level.SEVERE, "Row Num = {0}", row.getRowNum());
                if (formatter.formatCellValue(row.getCell(contractIdIndex)).trim().isEmpty())
                    break;

                Long contractId = Long.valueOf(formatter.formatCellValue(row.getCell(contractIdIndex)).trim());
                Long termId = Long.valueOf(formatter.formatCellValue(row.getCell(termIdIndex)).trim());
                Long housemaid = changeHousemaid ? Long.valueOf(formatter.formatCellValue(row.getCell(housemaidIdIndex)).trim()) : null;
                Long replacementId = changeReplacement ? Long.valueOf(formatter.formatCellValue(row.getCell(replacementIdIndex)).trim()) : null;
                String reason = changeReason ? formatter.formatCellValue(row.getCell(reasonIndex)).trim() : null;
                Double firstMonthPayment = changeFirstPayment ?
                        nf_in.parse(formatter.formatCellValue(row.getCell(firstMonthPaymentIndex)).trim()).doubleValue() : null;

                error = createNewCptHandleRow(contractId, termId, housemaid, replacementId, reason, firstMonthPayment);


            } catch (Exception e) {
                error = e.getMessage();
            }
            if(error != null && !error.isEmpty())
                stringBuilder.append("Error in line: ").append(rowNumber).append(", Error: ").append(error).append("</br>");
        }

        String result = stringBuilder.toString();
        if(result.isEmpty()) result = "All lines migrated successfully";
        return result;
    }

    // ACC-5579
    @Transactional
    public String createNewCptHandleRow(
            Long contractId, Long termId, Long housemaidId,
            Long replacementId, String reason, Double firstMonthPayment) {


        Contract contract = contractRepository.findOne(contractId);
        if (contract == null) return  "Contract not found, ID = " + contractId;

        ContractPaymentTerm oldCPT = contract.getActiveContractPaymentTerm();

        PaymentTermConfig ptc = paymentTermConfigRepository.findOne(termId);
        if (ptc == null) return  "Payment term config not found, ID = " + termId;

        Housemaid housemaid = housemaidId != null ? housemaidRepository.findOne(housemaidId) : contract.getHousemaid();
        if (housemaid == null) return  "housemaid not found, ID = " + housemaidId + " contract id " + contractId;

        Replacement replacement = null;
        if (replacementId != null) {
            replacement = replacementRepository.findOne(replacementId);
            if (replacement == null) return  "replacement not found, ID = " + replacementId;
        }

        ContractPaymentTermReason contractPaymentTermReason = oldCPT.getReason();
        if (reason != null) contractPaymentTermReason = ContractPaymentTermReason.valueOf(reason);

        if (firstMonthPayment == null) firstMonthPayment = oldCPT.getFirstMonthPayment();

        logger.log(Level.INFO, "oldCpt id: {0}; ptc id: {1}; housemaid id: {2}; replacement id: {3}; reason: {4}; firstMonthPayment: {5}",
                new Object[]{oldCPT.getId(), ptc.getId(), housemaid.getId(), replacementId != null ? replacementId : "null", reason, firstMonthPayment});

        ContractPaymentTerm newCPT = createNewCpt(oldCPT, replacement,
                contractPaymentTermReason, firstMonthPayment, housemaid, ptc);
        oldCPT.setActive(false);
        contractPaymentTermRepository.save(oldCPT);

        // move dds and cps
        directDebitRepository
                .findByContractPaymentTermAndStatusNotIn(oldCPT,
                        Arrays.asList(
                                DirectDebitStatus.CANCELED,
                                DirectDebitStatus.PENDING_FOR_CANCELLATION,
                                DirectDebitStatus.EXPIRED))
                .forEach(dd -> {
                    dd.setContractPaymentTerm(newCPT);
                    dd.getContractPayments().forEach(payment -> {
                        payment.setContractPaymentTerm(newCPT);
                    });

                    // Save DirectDebit which will cascade to ContractPayments
                    directDebitRepository.save(dd);
                });

        return "";
    }

    public ContractPaymentTerm createNewCpt(
            ContractPaymentTerm oldCpt, Replacement replacement, ContractPaymentTermReason reason,
            Double firstMonthPayment, Housemaid housemaid, PaymentTermConfig ptc) {

        ContractPaymentTerm newCPT = new ContractPaymentTerm();
        newCPT.setReplacement(replacement != null ? replacement : oldCpt.getReplacement());
        newCPT.setReason(reason);

        newCPT.setFirstMonthPayment(firstMonthPayment);
        newCPT.setHousemaid(housemaid);
        newCPT.setContract(oldCpt.getContract());
        newCPT.setPackageType(ptc.getPackageType());

        newCPT.setPaymentTermConfigWithValues(ptc);

        newCPT.setDiscountEffectiveAfter(ptc.getDiscountEffectiveAfter());
        newCPT.setDiscount(ptc.getDiscount());
        newCPT.setIsActive(true);

        newCPT.setAccountName(oldCpt.getAccountName());
        newCPT.setBank(oldCpt.getBank());
        newCPT.setBankName(oldCpt.getBankName());
        newCPT.setEid(oldCpt.getEid());
        newCPT.setIsProRated(oldCpt.isIsProRated());
        newCPT.setAdditionalDiscount(oldCpt.getAdditionalDiscount());
        newCPT.setAdditionalDiscountMonths(oldCpt.getAdditionalDiscountMonths());
        newCPT.setAdditionalDiscountNotes(oldCpt.getAdditionalDiscountNotes());
        newCPT.setSpouseWillSignDD(oldCpt.getSpouseWillSignDD());
        newCPT.setIbanNumber(oldCpt.getIbanNumber());

        newCPT.setIsEidRejected(oldCpt.getIsEidRejected());
        newCPT.setEidRejectionReason(oldCpt.getEidRejectionReason());

        newCPT.setIsIBANRejected(oldCpt.getIsIBANRejected());
        newCPT.setIbanRejectionReason(oldCpt.getIbanRejectionReason());

        newCPT.setIsAccountHolderRejected(oldCpt.getIsAccountHolderRejected());
        newCPT.setAccountNameRejectionReason(oldCpt.getAccountNameRejectionReason());

        //-ACC-2694
        newCPT.setReceiptSent(oldCpt.getReceiptSent());

        newCPT.setIsEOM(oldCpt.isIsEOM());
        newCPT.setProRatedDays(oldCpt.getProRatedDays() == 0 ? null : oldCpt.getProRatedDays());
        newCPT.setIsRemote(oldCpt.getIsRemote());
        newCPT.setDdMsgsDisabled(oldCpt.isDdMsgsDisabled());
        newCPT.setFixedByVAT(oldCpt.isFixedByVAT());
        newCPT.setSpouseWillSignDD(oldCpt.getSpouseWillSignDD());
        newCPT.setIsAfterSignsMsgSent(oldCpt.getIsAfterSignsMsgSent());
        newCPT.setSwitchingToPhilipino(oldCpt.getSwitchingToPhilipino());
        newCPT.setBankInfoTextBased(oldCpt.getBankInfoTextBased());
        newCPT.setSwitchedOrReplacedNationality(oldCpt.getSwitchedOrReplacedNationality());
        newCPT.setSwitchedBankAccount(oldCpt.getSwitchedBankAccount());
        newCPT.setAmendedDate(oldCpt.getAmendedDate());
        newCPT.setCreditNote(oldCpt.getCreditNote());
        newCPT.setCreditNoteMonths(oldCpt.getCreditNoteMonths());

        newCPT.setSwitchOrReplaceNationalityDate(replacement == null ? oldCpt.getSwitchOrReplaceNationalityDate() : replacement.getReplacmentDate());

        for(Attachment att : oldCpt.getAttachments()) {
            if (!att.getTag().equals(FILE_TAG_PAYMENTS_RECEIPT))
                newCPT.addAttachment(att);
        }
        newCPT = contractPaymentTermRepository.save(newCPT);

        // ACC-5795 visa fee and discount
        // Set a dummy value just to triggered OnUpdateMonthlyContractPaymentTermBR in Sales
        if (newCPT.getContract().isMaidVisa()) {
            newCPT = contractPaymentTermRepository.findOne(newCPT.getId());
            newCPT.setVisaFees(0.0);
            newCPT = contractPaymentTermRepository.save(newCPT);
        }

        // ACC-7421
        Setup.getApplicationContext().getBean(ContractPaymentTermServiceNew.class)
                .disableAllCcOfferLinkOfCptByBGT(oldCpt);
        return newCPT;
    }

    // ACC-8444
    public List<DirectDebitGenerationPlanPendingPayingViaCcReportCSV> handlePostponedPlanPendingPayingViaCreditCardAcc8444(boolean withOutSave) {

        DirectDebitGenerationPlanRepository directDebitGenerationPlanRepository = Setup.getRepository(DirectDebitGenerationPlanRepository.class);
        PaymentRepository paymentRepository = Setup.getRepository(PaymentRepository.class);
        FlowProcessorEntityRepository flowProcessorEntityRepository = Setup.getRepository(FlowProcessorEntityRepository.class);

        List<DirectDebitGenerationPlanPendingPayingViaCcReportCSV> plansCanceled = new ArrayList<>();
        List<DirectDebitGenerationPlanPendingPayingViaCcReportCSV> plansKeepThem = new ArrayList<>();
        List<DirectDebitGenerationPlanPendingPayingViaCcReportCSV> plansDateLastMonthAfterSendDate = new ArrayList<>();
        List<DirectDebitGenerationPlanPendingPayingViaCcReportCSV> plansInFuture = new ArrayList<>();


        directDebitGenerationPlanRepository.findByDdGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING_PAYING_VIA_CREDIT_CARD)
                .forEach(plan -> {
            DirectDebitGenerationPlanPendingPayingViaCcReportCSV d =
                    DirectDebitGenerationPlanPendingPayingViaCcReportCSV.builder()
                            .contractId(plan.getContract().getId())
                            .planId(plan.getId())
                            .ddGenerationDate(new LocalDate(plan.getDDGenerationDate()).toString("yyyy-MM-dd"))
                            .ddSendDate(new LocalDate(plan.getDDSendDate()).toString("yyyy-MM-dd"))
                            .amount(plan.getAmount().intValue())
                            .contractStatus(plan.getContract().getStatus().toString())
                            .contractDateOfTermination(plan.getContract().getDateOfTermination() == null ?
                                    "" : new LocalDate(plan.getContract().getDateOfTermination()).toString("yyyy-MM-dd"))
                            .typeOfPayment(plan.getContractPaymentType().getType().getName())
                            .build();

            // Check if match with received payment => canceled
            if (paymentRepository.existsReceivedPaymentsByContractAndAmountAndTypeAndDate(
                    plan.getContract().getId(), plan.getAmount(), plan.getDDSendDate(),
                    plan.getContractPaymentType().getType().getCode())) {
                d.setNotes("We found Payment received matched with this plan");
                d.setNewStatusPlan(DirectDebitGenerationPlan.DdGenerationPlanStatus.CANCELED.toString());
                plansCanceled.add(d);

                if (!withOutSave) {
                    plan.setddGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.CANCELED);
                    directDebitGenerationPlanRepository.save(plan);
                }
                return;
            }

            // Contract active and Has running flow => keep them
            if (plan.getContract().getStatus().equals(ContractStatus.ACTIVE) &&
                    flowProcessorEntityRepository.existsRunningFlowCoverPaymentByContractAndPaymentInfo(
                            plan.getContract().getId(), FlowEventConfig.FlowEventName.CLIENTS_PAYING_VIA_Credit_Card,
                            plan.getDDSendDate(), plan.getAmount(),
                            plan.getContractPaymentType().getType().getCode())) {
                d.setNotes("The contract is Active and there is a running flow covers this plan");
                d.setNewStatusPlan(DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING_PAYING_VIA_CREDIT_CARD.toString());
                plansKeepThem.add(d);
                return;
            }

            // Contract Active and send date <= Maximum day of this month => send email
            if (plan.getContract().getStatus().equals(ContractStatus.ACTIVE) &&
                    new LocalDate().dayOfMonth().withMaximumValue().isAfter(new LocalDate(plan.getDDSendDate()))) {
                d.setNotes("The contract is Active and Send date is during current month");
                d.setNewStatusPlan(DirectDebitGenerationPlan.DdGenerationPlanStatus.CANCELED.toString());
                plansDateLastMonthAfterSendDate.add(d);

                plan.setddGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.CANCELED);
                directDebitGenerationPlanRepository.save(plan);
                return;
            }

            // Contract not active and send date is after termination date
            if (Arrays.asList(ContractStatus.CANCELLED, ContractStatus.EXPIRED)
                    .contains(plan.getContract().getStatus()) &&
                    plan.getContract().getDateOfTermination() != null &&
                    new LocalDate(plan.getDDSendDate())
                            .isAfter(new LocalDate(plan.getContract().getDateOfTermination()))) {
                d.setNotes("Contract not active and send date is after termination date");
                d.setNewStatusPlan(DirectDebitGenerationPlan.DdGenerationPlanStatus.CANCELED.toString());
                plansCanceled.add(d);

                if (!withOutSave) {
                    plan.setddGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.CANCELED);
                    directDebitGenerationPlanRepository.save(plan);
                }
                return;
            }

            // Send date of plan in future => pending
            if (new LocalDate(plan.getDDSendDate()).isAfter(new LocalDate().dayOfMonth().withMaximumValue())) {
                d.setNotes("The Send date of plan in the Future");
                d.setNewStatusPlan(DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING.toString());
                plansInFuture.add(d);

                if (!withOutSave) {
                    plan.setddGenerationPlanStatus(DirectDebitGenerationPlan.DdGenerationPlanStatus.PENDING);
                    directDebitGenerationPlanRepository.save(plan);
                }
            }
        });

        List<DirectDebitGenerationPlanPendingPayingViaCcReportCSV> plansSendInEmail = new ArrayList<>();
        plansSendInEmail.addAll(plansCanceled);
        plansSendInEmail.addAll(plansKeepThem);
        plansSendInEmail.addAll(plansDateLastMonthAfterSendDate);
        plansSendInEmail.addAll(plansInFuture);
        return plansSendInEmail;
    }

    // ACC-8444
    public void sendEmailAcc8444(String email, List<DirectDebitGenerationPlanPendingPayingViaCcReportCSV> csvDataList) throws IOException {

        if (csvDataList.isEmpty()) return;

        String[] headers = {
                "Contract ID", "Plan ID",
                "DD Generation Date", "DD Send Date", "Amount",
                "Plan New Status", "Contract Status",
                "Date Of Termination", "Type of payment", "Notes"};

        String[] names = {
                "contractId", "planId",
                "ddGenerationDate", "ddSendDate", "amount",
                "newStatusPlan", "contractStatus",
                "contractDateOfTermination", "typeOfPayment", "notes"};

        File file = CsvHelper.generateCsv(csvDataList, DirectDebitGenerationPlanPendingPayingViaCcReportCSVProjection.class, headers, names,
                "Updates on Postponed Pending Paying Via Credit Card Report_" + new DateTime().toString("yyyy-MM-dd HH:mm:ss"), ".csv");

        TextEmail mail = new TextEmail("Updates on Postponed Pending Paying Via Credit Card Report", "");
        mail.addAttachement(file);
        Setup.getMailService()
                .sendEmail(new Recipient(email, email), mail, EmailReceiverType.Office_Staff);
    }

    // ACC-8851
    // to be checked with Shaban
    public void handleUpdatesRelatedRejectionFlowAcc8851() {
        int paperModeThreshold = Integer.parseInt(
                Setup.getParameter(Setup.getCurrentModule(),
                        AccountingModule.PARAMETER_SIGNING_PAPER_MODE_THRESHOLD_TRIAL));

        String select = "select new Map(todo.id as todoId) from DirectDebitRejectionToDo todo ";
        String conditions =  "where todo.completed = false and todo.stopped = false" +
                "group by todo.id";
        List<String> joins =  Arrays.asList(
                "inner join DirectDebit d on d.directDebitRejectionToDo = todo ",
                "inner join DirectDebit d on d.directDebitBouncingRejectionToDo = todo ");

        Map<String, Object> params = new HashMap<>();
        params.put("p0", paperModeThreshold);


        for (String join : joins) {
            List<Map> l = new SelectQuery<>(select + join + conditions, "", Map.class, params).execute();

            logger.info("rejectionToDoIds size: " + l.size() + "; List (rejectionToDo, cpt): " + l);

            DirectDebitRejectionToDoRepository directDebitRejectionToDoRepository = Setup.getRepository(DirectDebitRejectionToDoRepository.class);
            ContractService contractService = Setup.getApplicationContext().getBean(ContractService.class);
            for (Map<String, Object> m : l) {
                try {
                    DirectDebitRejectionToDo rejectionToDo = directDebitRejectionToDoRepository.findOne((Long) m.get("todoId"));
                    DirectDebit dd = rejectionToDo.getLastDirectDebit();
                    if (rejectionToDo.getTrials() < paperModeThreshold &&
                            !dd.getContractPaymentTerm().getContract().isSigningPaperMode()) {
                        continue;
                    }

                    if (!dd.getContractPaymentTerm().getContract().isSigningPaperMode()) {
                        contractService.updatePaperModeAsync(dd.getContractPaymentTerm(), true);
                    }
                    if (rejectionToDo.getTrials() == paperModeThreshold) continue;

                    rejectionToDo.setTrials(paperModeThreshold);
                    directDebitRejectionToDoRepository.save(rejectionToDo);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    // ACC-8851
    // to be checked with Shaban
    public void handleAllContractsPEDInFutureAndIsSFTCausedRejectionFlowAcc8851(Map<String, Object> body) {

        String q = (String) body.getOrDefault("query", null);
        Map<String, Object> params = (Map<String, Object>) body.getOrDefault("parameters", new HashMap<>());
        if (q == null) {
            q = "select new Map(c.id as contractId, c.contractProspectType.code as code, dd.id as ddId, todo.id as todoId) " +
                    "from DirectDebit dd  " +
                    "join dd.directDebitRejectionToDo todo " +
                    "join dd.contractPaymentTerm cpt " +
                    "join cpt.contract c " +
                    "where cpt.isActive = true and c.status = :p0 and c.paidEndDate > :p1 and " +
                        "c.isScheduledForTermination = true and c.scheduledDateOfTermination is not null and " +
                        "c.reasonOfTerminationList.code in :p2 and todo.leadingRejectionFlow = true";

            params = new HashMap<>();
            params.put("p0", ContractStatus.ACTIVE);
            params.put("p1", new LocalDate().toDate());
            params.put("p2", ContractScheduleForTerminationUtils.getDDRejectionFlowTerminationReasons());
        }

        List<Map> l = new SelectQuery<>(q, "", Map.class, params)
                .execute();

        logger.info("contracts size: " + l.size() + "; contract ids: " + l);

        SelectQuery<DDMessaging> query = new SelectQuery<>(DDMessaging.class);
        query.filterBy("event", "=", DDMessagingType.Termination);
        query.filterBy("scheduleTermCategory", "=", DirectDebitMessagingScheduleTermCategory.GToday);
        query.filterBy("sendPayTabMessage", "=", true);
        List<DDMessaging> ddMessagingList = query.execute();

        ContractService contractService = Setup.getApplicationContext().getBean(ContractService.class);
        DirectDebitRepository directDebitRepository = Setup.getRepository(DirectDebitRepository.class);
        DirectDebitRejectionFlowService directDebitRejectionFlowService = Setup.getApplicationContext().getBean(DirectDebitRejectionFlowService.class);
        for (Map<String, Object> m : l) {
            try {
                contractService.retractContractTermination((Long) m.get("contractId"));

                // ACC-8851 TODO create DD Messaging Todo, the event of DDMessaing is Termination
                DDMessaging ddMessaging = ddMessagingList.stream()
                        .filter(d -> d.getContractProspectTypes().contains((String) m.get("code")))
                        .findFirst()
                        .orElse(null);

                // TODO to be checked
                DirectDebit directDebit = directDebitRepository.findOne((Long) m.get("ddId"));                                                                      // directDebit, rejectionToDo
                directDebitRejectionFlowService.handleDelayTerminationMessageBeforePaidEndDate(
                        ddMessaging, directDebit, directDebit.getDirectDebitRejectionToDo());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}